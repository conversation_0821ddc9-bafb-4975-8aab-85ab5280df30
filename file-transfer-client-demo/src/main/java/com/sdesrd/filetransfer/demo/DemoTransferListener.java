package com.sdesrd.filetransfer.demo;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.util.FileUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 演示用的传输监听器
 * 
 * 这个监听器用于演示文件传输过程中的进度监控和事件处理功能
 * 
 */
@Slf4j
public class DemoTransferListener implements TransferListener {

    /** 进度更新间隔（毫秒） */
    private static final long PROGRESS_UPDATE_INTERVAL_MS = 1000;
    
    /** 进度显示阈值（百分比） */
    private static final double PROGRESS_DISPLAY_THRESHOLD = 5.0;
    
    /** 上次进度更新时间 */
    private volatile long lastProgressUpdateTime = 0;
    
    /** 上次显示的进度百分比 */
    private volatile double lastDisplayedProgress = 0.0;
    
    /** 传输开始时间 */
    private volatile long transferStartTime = 0;
    
    /** 进度回调计数器 */
    private final AtomicInteger progressCallCount = new AtomicInteger(0);
    
    /** 传输的字节数 */
    private final AtomicLong transferredBytes = new AtomicLong(0);
    
    /** 最终进度信息 */
    private volatile TransferProgress finalProgress;
    
    /** 是否启用详细日志 */
    private final boolean verboseLogging;
    
    /**
     * 默认构造函数 - 启用详细日志
     */
    public DemoTransferListener() {
        this(true);
    }
    
    /**
     * 构造函数
     * 
     * @param verboseLogging 是否启用详细日志
     */
    public DemoTransferListener(boolean verboseLogging) {
        this.verboseLogging = verboseLogging;
    }

    @Override
    public void onStart(TransferProgress progress) {
        transferStartTime = System.currentTimeMillis();
        lastProgressUpdateTime = transferStartTime;
        lastDisplayedProgress = 0.0;
        progressCallCount.set(0);
        transferredBytes.set(0);
        finalProgress = null;

        if (verboseLogging) {
            log.info("传输开始 - 文件名: {}, 总大小: {}",
                    progress.getFileName(), 
                    progress.getTotalSize() != null ? FileUtils.formatFileSize(progress.getTotalSize()) : "未知");
        }

        System.out.printf("开始传输: %s (%s)%n", 
                progress.getFileName(), 
                progress.getTotalSize() != null ? FileUtils.formatFileSize(progress.getTotalSize()) : "未知");
    }

    @Override
    public void onProgress(TransferProgress progress) {
        progressCallCount.incrementAndGet();
        transferredBytes.set(progress.getTransferredSize());

        long currentTime = System.currentTimeMillis();
        double currentProgress = progress.getProgress();

        // 控制进度显示频率
        boolean shouldDisplay = false;

        // 时间间隔控制
        if (currentTime - lastProgressUpdateTime >= PROGRESS_UPDATE_INTERVAL_MS) {
            shouldDisplay = true;
        }

        // 进度阈值控制
        if (currentProgress - lastDisplayedProgress >= PROGRESS_DISPLAY_THRESHOLD) {
            shouldDisplay = true;
        }

        // 完成时必须显示
        if (currentProgress >= 100.0) {
            shouldDisplay = true;
        }

        if (shouldDisplay) {
            displayProgress(progress, currentTime);
            lastProgressUpdateTime = currentTime;
            lastDisplayedProgress = currentProgress;
        }

        if (verboseLogging && log.isDebugEnabled()) {
            log.debug("传输进度 - 进度: {:.2f}%, 已传输: {}, 总大小: {}",
                    progress.getProgress(),
                    FileUtils.formatFileSize(progress.getTransferredSize()),
                    progress.getTotalSize() != null ? FileUtils.formatFileSize(progress.getTotalSize()) : "未知");
        }
    }

    @Override
    public void onCompleted(TransferProgress progress) {
        long transferEndTime = System.currentTimeMillis();
        long totalDuration = transferEndTime - transferStartTime;

        if (verboseLogging) {
            log.info("传输完成 - 文件名: {}, 总大小: {}, 耗时: {}ms",
                    progress.getFileName(), 
                    progress.getTotalSize() != null ? FileUtils.formatFileSize(progress.getTotalSize()) : "未知", 
                    totalDuration);
        }

        // 计算平均传输速度
        long totalSize = progress.getTotalSize() != null ? progress.getTotalSize() : progress.getTransferredSize();
        double averageSpeedBytesPerSecond = totalDuration > 0 ? (totalSize * 1000.0) / totalDuration : 0;

        System.out.printf("✅ 传输完成: %s, 耗时: %d毫秒, 平均速度: %s/s%n",
                progress.getFileName(), totalDuration, FileUtils.formatFileSize((long) averageSpeedBytesPerSecond));

        // 保存最终进度信息
        finalProgress = progress;
    }

    @Override
    public void onError(TransferProgress progress, Throwable error) {
        long transferEndTime = System.currentTimeMillis();
        long totalDuration = transferEndTime - transferStartTime;

        if (verboseLogging) {
            log.error("传输失败 - 文件名: {}, 错误信息: {}, 耗时: {}ms",
                    progress.getFileName(), error.getMessage(), totalDuration, error);
        }

        System.out.printf("❌ 传输失败: %s, 错误: %s, 耗时: %d毫秒%n",
                progress.getFileName(), error.getMessage(), totalDuration);
    }

    @Override
    public void onPaused(TransferProgress progress) {
        long transferEndTime = System.currentTimeMillis();
        long totalDuration = transferEndTime - transferStartTime;

        if (verboseLogging) {
            log.info("传输暂停 - 文件名: {}, 耗时: {}ms",
                    progress.getFileName(), totalDuration);
        }

        System.out.printf("⚠️ 传输暂停: %s, 耗时: %d毫秒%n", progress.getFileName(), totalDuration);
    }

    /**
     * 显示传输进度
     * 
     * @param progress 进度信息
     * @param currentTime 当前时间
     */
    private void displayProgress(TransferProgress progress, long currentTime) {
        long elapsedTime = currentTime - transferStartTime;
        
        // 创建进度条
        String progressBar = createProgressBar(progress.getProgress());
        
        // 计算剩余时间估算
        String remainingTimeStr = calculateRemainingTime(progress.getProgress(), elapsedTime);
        
        // 计算当前速度
        long currentSpeed = 0;
        if (progress.getSpeed() != null) {
            currentSpeed = progress.getSpeed();
        } else {
            // 如果没有速度信息，根据时间差计算
            elapsedTime = currentTime - transferStartTime;
            if (elapsedTime > 0) {
                currentSpeed = (progress.getTransferredSize() * 1000) / elapsedTime;
            }
        }

        System.out.printf("\r进度: %s %.1f%% (%s/%s) 速度: %s/s %s",
                progressBar,
                progress.getProgress(),
                FileUtils.formatFileSize(progress.getTransferredSize()),
                progress.getTotalSize() != null ? FileUtils.formatFileSize(progress.getTotalSize()) : "未知",
                FileUtils.formatFileSize(currentSpeed),
                remainingTimeStr);
        
        // 如果传输完成，换行
        if (progress.getProgress() >= 100.0) {
            System.out.println();
        }
    }

    /**
     * 创建进度条字符串
     * 
     * @param progress 进度百分比
     * @return 进度条字符串
     */
    private String createProgressBar(double progress) {
        int totalBars = 20;
        int completedBars = (int) (progress / 100.0 * totalBars);
        
        StringBuilder progressBar = new StringBuilder("[");
        for (int i = 0; i < totalBars; i++) {
            if (i < completedBars) {
                progressBar.append("=");
            } else if (i == completedBars && progress < 100.0) {
                progressBar.append(">");
            } else {
                progressBar.append(" ");
            }
        }
        progressBar.append("]");
        
        return progressBar.toString();
    }

    /**
     * 计算剩余时间估算
     * 
     * @param progress 当前进度百分比
     * @param elapsedTime 已用时间（毫秒）
     * @return 剩余时间字符串
     */
    private String calculateRemainingTime(double progress, long elapsedTime) {
        if (progress <= 0 || progress >= 100.0) {
            return "";
        }
        
        // 估算剩余时间
        long estimatedTotalTime = (long) (elapsedTime / (progress / 100.0));
        long remainingTime = estimatedTotalTime - elapsedTime;
        
        if (remainingTime <= 0) {
            return "";
        }
        
        // 格式化剩余时间
        if (remainingTime < 1000) {
            return String.format("剩余: %dms", remainingTime);
        } else if (remainingTime < 60000) {
            return String.format("剩余: %ds", remainingTime / 1000);
        } else {
            long minutes = remainingTime / 60000;
            long seconds = (remainingTime % 60000) / 1000;
            return String.format("剩余: %dm%ds", minutes, seconds);
        }
    }

    /**
     * 获取进度回调次数
     * 
     * @return 进度回调次数
     */
    public int getProgressCallCount() {
        return progressCallCount.get();
    }

    /**
     * 获取已传输字节数
     * 
     * @return 已传输字节数
     */
    public long getTransferredBytes() {
        return transferredBytes.get();
    }

    /**
     * 获取最终进度信息
     * 
     * @return 最终进度信息
     */
    public TransferProgress getFinalProgress() {
        return finalProgress;
    }

    /**
     * 获取传输开始时间
     * 
     * @return 传输开始时间（毫秒时间戳）
     */
    public long getTransferStartTime() {
        return transferStartTime;
    }

    /**
     * 重置监听器状态
     */
    public void reset() {
        lastProgressUpdateTime = 0;
        lastDisplayedProgress = 0.0;
        transferStartTime = 0;
        progressCallCount.set(0);
        transferredBytes.set(0);
        finalProgress = null;
    }

    /**
     * 是否启用了详细日志
     * 
     * @return 是否启用详细日志
     */
    public boolean isVerboseLogging() {
        return verboseLogging;
    }
}
