package com.sdesrd.filetransfer.server.service;

import java.io.File;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.config.UserConfig;
import com.sdesrd.filetransfer.server.dto.FileInfo;
import com.sdesrd.filetransfer.server.dto.FileMetadata;
import com.sdesrd.filetransfer.server.entity.FileTransferRecord;
import com.sdesrd.filetransfer.server.exception.FileTransferException;
import com.sdesrd.filetransfer.server.mapper.FileTransferRecordMapper;
import com.sdesrd.filetransfer.server.util.FileUtils;
import com.sdesrd.filetransfer.server.util.UlidUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 数据库故障回退服务类
 * 
 * <p>提供SQLite数据库故障时的回退机制，通过扫描磁盘上的info.json元数据文件
 * 来提供文件上传、下载和查询功能。确保系统在数据库不可用时仍能正常运行。</p>
 * 
 * <p>主要功能包括：</p>
 * <ul>
 *   <li>数据库健康状态检测</li>
 *   <li>基于info.json的文件查找和信息获取</li>
 *   <li>磁盘扫描和文件重建</li>
 *   <li>回退模式下的文件服务</li>
 * </ul>
 * 
 */
@Slf4j
@Service
public class DatabaseFallbackService {
    
    /**
     * 数据库健康检查超时时间（毫秒）
     */
    private static final long DATABASE_HEALTH_CHECK_TIMEOUT_MS = 5000L;
    
    /**
     * 数据库健康检查间隔时间（毫秒）
     */
    private static final long DATABASE_HEALTH_CHECK_INTERVAL_MS = 30000L;
    
    /**
     * 最大扫描文件数量限制（防止内存溢出）
     */
    private static final int MAX_SCAN_FILES_LIMIT = 1000000;
    
    /**
     * 数据库健康状态缓存
     */
    private final AtomicBoolean databaseHealthy = new AtomicBoolean(true);
    
    /**
     * 最后一次健康检查时间
     */
    private final AtomicLong lastHealthCheckTime = new AtomicLong(0);
    
    @Autowired
    private FileTransferRecordMapper transferRecordMapper;
    
    @Autowired
    private FileTransferProperties properties;
    
    @Autowired
    private FileMetadataService metadataService;
    
    /**
     * 检查数据库健康状态
     * 
     * @return 如果数据库可用则返回true，否则返回false
     */
    public boolean isDatabaseHealthy() {
        long currentTime = System.currentTimeMillis();
        long lastCheck = lastHealthCheckTime.get();
        
        // 如果距离上次检查时间未超过间隔，返回缓存的结果
        if (currentTime - lastCheck < DATABASE_HEALTH_CHECK_INTERVAL_MS) {
            return databaseHealthy.get();
        }
        
        // 执行健康检查
        boolean healthy = performDatabaseHealthCheck();
        databaseHealthy.set(healthy);
        lastHealthCheckTime.set(currentTime);
        
        return healthy;
    }
    
    /**
     * 强制刷新数据库健康状态
     * 
     * @return 当前数据库健康状态
     */
    public boolean refreshDatabaseHealth() {
        boolean healthy = performDatabaseHealthCheck();
        databaseHealthy.set(healthy);
        lastHealthCheckTime.set(System.currentTimeMillis());
        
        log.info("强制刷新数据库健康状态: {}", healthy ? "健康" : "故障");
        return healthy;
    }
    
    /**
     * 基于fileId查找文件信息（回退模式）
     * 
     * @param fileId 文件标识符
     * @param username 用户名
     * @return 文件信息，如果不存在则返回null
     */
    public FileInfo findFileByIdFallback(String fileId, String username) {
        if (!StringUtils.hasText(fileId)) {
            return null;
        }
        
        try {
            // 获取用户配置的存储路径
            UserConfig userConfig = properties.getUserConfig(username);
            String storagePath = userConfig.getStoragePath();

            // 尝试读取info.json元数据文件
            FileMetadata metadata = metadataService.readMetadata(fileId, storagePath);
            if (metadata != null && metadata.isComplete()) {
                return convertMetadataToFileInfo(metadata);
            }

            // 如果没有元数据文件，尝试基于路径规则查找物理文件
            String physicalFilePath = tryFindPhysicalFile(fileId, storagePath);
            if (physicalFilePath != null) {
                return buildFileInfoFromPhysicalFile(fileId, physicalFilePath);
            }
            
            log.debug("回退模式：未找到文件 - fileId: {}, 用户: {}", fileId, username);
            return null;
            
        } catch (Exception e) {
            log.error("回退模式查找文件失败 - fileId: {}, 用户: {}", fileId, username, e);
            return null;
        }
    }
    
    /**
     * 基于相对路径查找文件信息（回退模式）
     * 
     * @param relativePath 相对路径
     * @param username 用户名
     * @return 文件信息，如果不存在则返回null
     */
    public FileInfo findFileByPathFallback(String relativePath, String username) {
        if (!StringUtils.hasText(relativePath)) {
            return null;
        }
        
        try {
            String storagePath = properties.getDefaultConfig().getStoragePath();
            String absolutePath = Paths.get(storagePath, relativePath).toString();
            
            // 检查文件是否存在
            File physicalFile = new File(absolutePath);
            if (!physicalFile.exists() || !physicalFile.isFile()) {
                log.debug("回退模式：物理文件不存在 - 路径: {}", absolutePath);
                return null;
            }
            
            // 尝试从路径中提取fileId
            String fileId = extractFileIdFromPath(relativePath);
            if (fileId != null) {
                // 尝试读取对应的元数据文件
                FileMetadata metadata = metadataService.readMetadata(fileId, storagePath);
                if (metadata != null && metadata.isComplete()) {
                    return convertMetadataToFileInfo(metadata);
                }
            }
            
            // 如果没有元数据文件，基于物理文件构建基本信息
            return buildFileInfoFromPhysicalFile(fileId, absolutePath);
            
        } catch (Exception e) {
            log.error("回退模式通过路径查找文件失败 - 路径: {}, 用户: {}", relativePath, username, e);
            return null;
        }
    }
    
    /**
     * 扫描存储目录并重建数据库（回退模式辅助功能）
     * 支持多用户存储路径扫描
     *
     * @return 重建结果统计信息
     */
    public Map<String, Object> scanAndRebuildFromMetadata() {
        Map<String, Object> result = new HashMap<>();

        int totalScannedFiles = 0;
        int totalRebuiltRecords = 0;
        int totalSkippedFiles = 0;
        List<String> errors = new ArrayList<>();
        Map<String, Object> userResults = new HashMap<>();

        try {
            log.info("开始从元数据文件扫描重建数据库 - 支持多用户存储路径");

            // 收集所有需要扫描的唯一路径，避免重复扫描
            Map<String, String> uniquePaths = new HashMap<>(); // path -> description
            Set<String> scannedPaths = new HashSet<>();

            // 1. 添加默认存储路径
            String defaultStoragePath = properties.getDefaultConfig().getStoragePath();
            uniquePaths.put(defaultStoragePath, "默认配置");

            // 2. 收集所有用户的唯一存储路径
            if (properties.getUsers() != null && !properties.getUsers().isEmpty()) {
                log.info("分析 {} 个用户的存储路径配置", properties.getUsers().size());

                for (Map.Entry<String, UserConfig> userEntry : properties.getUsers().entrySet()) {
                    String username = userEntry.getKey();
                    UserConfig userConfig = userEntry.getValue();

                    if (userConfig.getStoragePath() != null) {
                        String userStoragePath = userConfig.getStoragePath();

                        if (!uniquePaths.containsKey(userStoragePath)) {
                            // 新的唯一路径
                            uniquePaths.put(userStoragePath, "用户:" + username);
                            log.debug("添加用户 {} 的存储路径: {}", username, userStoragePath);
                        } else {
                            // 路径已存在，记录共享信息
                            String existingDesc = uniquePaths.get(userStoragePath);
                            uniquePaths.put(userStoragePath, existingDesc + ",用户:" + username);
                            log.debug("用户 {} 与 {} 共享存储路径: {}", username, existingDesc, userStoragePath);
                        }
                    } else {
                        log.debug("用户 {} 未配置存储路径，将使用默认路径", username);
                    }
                }
            } else {
                log.info("未配置用户，仅扫描默认存储路径");
            }

            log.info("去重后需要扫描 {} 个唯一存储路径", uniquePaths.size());

            // 3. 扫描所有唯一路径
            for (Map.Entry<String, String> pathEntry : uniquePaths.entrySet()) {
                String storagePath = pathEntry.getKey();
                String pathDescription = pathEntry.getValue();

                log.info("扫描存储路径: {} ({})", storagePath, pathDescription);

                try {
                    Map<String, Object> pathResult = scanSingleStoragePath(storagePath, pathDescription, errors);

                    // 使用路径作为key存储结果，避免用户名冲突
                    String resultKey = storagePath.equals(defaultStoragePath) ? "default" : storagePath;
                    userResults.put(resultKey, pathResult);

                    totalScannedFiles += (Integer) pathResult.getOrDefault("scannedFiles", 0);
                    totalRebuiltRecords += (Integer) pathResult.getOrDefault("rebuiltRecords", 0);
                    totalSkippedFiles += (Integer) pathResult.getOrDefault("skippedFiles", 0);

                    scannedPaths.add(storagePath);

                } catch (Exception e) {
                    String error = String.format("扫描存储路径失败 - %s (%s): %s",
                                                storagePath, pathDescription, e.getMessage());
                    errors.add(error);
                    log.warn(error, e);
                }
            }

            result.put("success", true);
            result.put("message", "多用户存储路径扫描重建完成");

        } catch (Exception e) {
            log.error("扫描重建数据库失败", e);
            result.put("success", false);
            result.put("message", "扫描重建失败: " + e.getMessage());
        }

        result.put("scannedFiles", totalScannedFiles);
        result.put("rebuiltRecords", totalRebuiltRecords);
        result.put("skippedFiles", totalSkippedFiles);
        result.put("errors", errors);
        result.put("userResults", userResults);

        log.info("多用户扫描重建完成 - 总扫描: {}, 总重建: {}, 总跳过: {}, 错误: {}",
                totalScannedFiles, totalRebuiltRecords, totalSkippedFiles, errors.size());

        return result;
    }

    /**
     * 扫描单个存储路径并重建数据库记录
     *
     * @param storagePath 存储路径
     * @param pathDescription 路径描述（用于日志）
     * @param errors 错误列表
     * @return 扫描结果
     */
    private Map<String, Object> scanSingleStoragePath(String storagePath, String pathDescription, List<String> errors) {
        Map<String, Object> result = new HashMap<>();
        int scannedFiles = 0;
        int rebuiltRecords = 0;
        int skippedFiles = 0;

        try {
            File baseDir = new File(storagePath);
            if (!baseDir.exists() || !baseDir.isDirectory()) {
                String warning = String.format("%s 存储目录不存在或不是目录: %s", pathDescription, storagePath);
                log.warn(warning);
                errors.add(warning);

                result.put("scannedFiles", 0);
                result.put("rebuiltRecords", 0);
                result.put("skippedFiles", 0);
                result.put("warning", warning);
                return result;
            }

            log.info("开始扫描 {} 存储路径: {}", pathDescription, storagePath);

            // 遍历年月目录（YYYYMM格式）
            try (DirectoryStream<Path> yearMonthDirs = Files.newDirectoryStream(baseDir.toPath())) {
                for (Path yearMonthDir : yearMonthDirs) {
                    if (!Files.isDirectory(yearMonthDir)) {
                        continue;
                    }

                    String yearMonth = yearMonthDir.getFileName().toString();
                    log.debug("扫描年月目录: {} - {}", pathDescription, yearMonth);

                    // 遍历fileId目录
                    try (DirectoryStream<Path> fileIdDirs = Files.newDirectoryStream(yearMonthDir)) {
                        for (Path fileIdDir : fileIdDirs) {
                            if (!Files.isDirectory(fileIdDir)) {
                                continue;
                            }

                            scannedFiles++;
                            String fileId = fileIdDir.getFileName().toString();

                            try {
                                // 读取info.json元数据文件
                                FileMetadata metadata = metadataService.readMetadata(fileId, storagePath);
                                if (metadata != null && metadata.isComplete()) {
                                    // 检查数据库中是否已存在该记录
                                    if (isDatabaseHealthy() && !fileRecordExists(fileId)) {
                                        // 将元数据转换为数据库记录并插入
                                        // 注意：这里使用当前存储路径构建正确的文件路径
                                        FileTransferRecord record = convertMetadataToRecordWithStoragePath(metadata, storagePath);
                                        transferRecordMapper.insert(record);
                                        rebuiltRecords++;

                                        log.debug("重建数据库记录 - {} - fileId: {}, 原文件名: {}",
                                                 pathDescription, fileId, metadata.getOriginalFileName());
                                    } else {
                                        skippedFiles++;
                                        log.debug("跳过已存在记录 - {} - fileId: {}", pathDescription, fileId);
                                    }
                                } else {
                                    skippedFiles++;
                                    log.debug("跳过无效元数据文件 - {} - fileId: {}", pathDescription, fileId);
                                }

                            } catch (Exception e) {
                                skippedFiles++;
                                String error = String.format("处理文件失败 - %s - fileId: %s, 错误: %s",
                                                            pathDescription, fileId, e.getMessage());
                                errors.add(error);
                                log.warn(error, e);
                            }
                        }
                    }
                }
            }

            log.info("{} 扫描完成 - 扫描: {}, 重建: {}, 跳过: {}",
                    pathDescription, scannedFiles, rebuiltRecords, skippedFiles);

        } catch (Exception e) {
            String error = String.format("扫描 %s 失败: %s", pathDescription, e.getMessage());
            log.error(error, e);
            errors.add(error);
        }

        result.put("scannedFiles", scannedFiles);
        result.put("rebuiltRecords", rebuiltRecords);
        result.put("skippedFiles", skippedFiles);
        result.put("storagePath", storagePath);
        result.put("pathDescription", pathDescription);

        return result;
    }
    
    /**
     * 执行数据库健康检查
     * 
     * @return 数据库是否健康
     */
    private boolean performDatabaseHealthCheck() {
        try {
            long startTime = System.currentTimeMillis();
            
            // 尝试执行简单的数据库查询
            QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
            query.last("LIMIT 1");
            transferRecordMapper.selectList(query);
            
            long duration = System.currentTimeMillis() - startTime;
            
            if (duration > DATABASE_HEALTH_CHECK_TIMEOUT_MS) {
                log.warn("数据库响应缓慢 - 耗时: {}ms, 阈值: {}ms", duration, DATABASE_HEALTH_CHECK_TIMEOUT_MS);
                return false;
            }
            
            log.debug("数据库健康检查通过 - 耗时: {}ms", duration);
            return true;
            
        } catch (DataAccessException e) {
            log.warn("数据库健康检查失败 - 数据访问异常: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("数据库健康检查异常", e);
            return false;
        }
    }
    
    /**
     * 尝试基于新的ULID存储规则查找物理文件
     * 
     * @param fileId 文件标识符
     * @param storagePath 存储根路径
     * @return 物理文件路径，如果不存在则返回null
     */
    private String tryFindPhysicalFile(String fileId, String storagePath) {
        try {
            // 如果fileId是ULID格式，从中提取年月信息
            if (UlidUtils.isValidUlid(fileId)) {
                String yearMonth = UlidUtils.extractYearMonth(fileId);
                if (yearMonth != null) {
                    Path fileIdDir = Paths.get(storagePath, yearMonth, fileId);
                    if (Files.exists(fileIdDir) && Files.isDirectory(fileIdDir)) {
                        // 查找目录下的第一个普通文件（排除info.json）
                        try (DirectoryStream<Path> files = Files.newDirectoryStream(fileIdDir)) {
                            for (Path file : files) {
                                if (Files.isRegularFile(file) && !file.getFileName().toString().equals("info.json")) {
                                    return file.toString();
                                }
                            }
                        }
                    }
                }
            }
            
            return null;
            
        } catch (Exception e) {
            log.debug("查找物理文件失败 - fileId: {}", fileId, e);
            return null;
        }
    }
    
    /**
     * 从相对路径中提取fileId
     * 
     * @param relativePath 相对路径
     * @return fileId，如果无法提取则返回null
     */
    private String extractFileIdFromPath(String relativePath) {
        try {
            Path path = Paths.get(relativePath);
            // 相对路径格式：YYYYMM/fileId/fileName
            if (path.getNameCount() >= 2) {
                return path.getName(1).toString();
            }
            return null;
        } catch (Exception e) {
            log.debug("从路径提取fileId失败 - 路径: {}", relativePath, e);
            return null;
        }
    }
    
    /**
     * 基于物理文件构建文件信息
     * 
     * @param fileId 文件ID
     * @param filePath 物理文件路径
     * @return 文件信息
     */
    private FileInfo buildFileInfoFromPhysicalFile(String fileId, String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                return null;
            }
            
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileId(fileId != null ? fileId : "unknown");
            fileInfo.setFileName(file.getName());
            fileInfo.setFileSize(file.length());
            fileInfo.setFormattedSize(FileUtils.formatFileSize(file.length()));
            fileInfo.setLastModified(file.lastModified());
            fileInfo.setCanRead(file.canRead());
            fileInfo.setCanWrite(file.canWrite());
            
            // 计算相对路径
            String storagePath = properties.getDefaultConfig().getStoragePath();
            if (filePath.startsWith(storagePath)) {
                String relativePath = filePath.substring(storagePath.length() + 1);
                // 统一使用正斜杠作为路径分隔符，确保跨平台兼容性
                fileInfo.setRelativePath(relativePath.replace(File.separator, "/"));
            }
            
            // 检测文件类型
            String extension = getFileExtension(file.getName());
            fileInfo.setExtension(extension);
            fileInfo.setFileType(detectFileType(file.getName()));
            
            log.debug("基于物理文件构建文件信息 - fileId: {}, 文件名: {}, 大小: {}", 
                     fileId, file.getName(), file.length());
            
            return fileInfo;
            
        } catch (Exception e) {
            log.error("基于物理文件构建文件信息失败 - fileId: {}, 路径: {}", fileId, filePath, e);
            return null;
        }
    }
    
    /**
     * 将元数据转换为文件信息
     * 
     * @param metadata 文件元数据
     * @return 文件信息
     */
    private FileInfo convertMetadataToFileInfo(FileMetadata metadata) {
        FileInfo fileInfo = new FileInfo();
        
        fileInfo.setFileId(metadata.getFileId());
        fileInfo.setFileName(metadata.getOriginalFileName() != null ? 
                            metadata.getOriginalFileName() : metadata.getPhysicalFileName());
        fileInfo.setFileSize(metadata.getFileSize());
        fileInfo.setFormattedSize(metadata.getFormattedSize());
        fileInfo.setFileType(metadata.getFileType());
        fileInfo.setUploadTime(metadata.getUploadTime());
        fileInfo.setRelativePath(metadata.getRelativePath());
        
        if (metadata.getLastModified() != null) {
            try {
                // 简化的时间解析
                fileInfo.setLastModified(System.currentTimeMillis());
            } catch (Exception e) {
                fileInfo.setLastModified(System.currentTimeMillis());
            }
        }
        
        fileInfo.setCanRead(true);
        fileInfo.setCanWrite(false); // 上传完成的文件默认为只读
        fileInfo.setExtension(metadata.getFileExtension());
        
        return fileInfo;
    }
    
    /**
     * 检查文件记录是否存在
     * 
     * @param fileId 文件ID
     * @return 如果存在则返回true
     */
    private boolean fileRecordExists(String fileId) {
        try {
            QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
            query.eq("file_id", fileId);
            return transferRecordMapper.selectCount(query) > 0;
        } catch (Exception e) {
            log.debug("检查文件记录存在性失败 - fileId: {}", fileId, e);
            return false;
        }
    }
    
    /**
     * 将元数据转换为数据库记录
     *
     * @param metadata 文件元数据
     * @return 文件传输记录
     */
    private FileTransferRecord convertMetadataToRecord(FileMetadata metadata) {
        // 使用默认存储路径进行转换（保持向后兼容）
        return convertMetadataToRecordWithStoragePath(metadata, properties.getDefaultConfig().getStoragePath());
    }

    /**
     * 将元数据转换为数据库记录，使用指定的存储路径
     *
     * @param metadata 文件元数据
     * @param storagePath 存储路径
     * @return 文件传输记录
     */
    private FileTransferRecord convertMetadataToRecordWithStoragePath(FileMetadata metadata, String storagePath) {
        FileTransferRecord record = new FileTransferRecord();

        record.setId(metadata.getTransferId());
        record.setFileId(metadata.getFileId());
        record.setFileName(metadata.getPhysicalFileName());
        record.setOriginalFileName(metadata.getOriginalFileName());
        record.setFileSize(metadata.getFileSize());
        record.setFileType(metadata.getFileType());
        record.setStatus(metadata.getStatus());
        record.setClientIp(metadata.getClientIp());
        record.setCreateTime(metadata.getCreateTime());
        record.setUpdateTime(metadata.getLastModified());
        record.setCompleteTime(metadata.getUploadTime());
        record.setTotalChunks(metadata.getTotalChunks());
        record.setCompletedChunks(metadata.getCompletedChunks());
        record.setTransferredSize(metadata.getFileSize());
        record.setExtInfo(metadata.getExtInfo());

        // 构建文件路径 - 使用指定的存储路径而非默认路径
        if (metadata.getRelativePath() != null) {
            record.setFilePath(Paths.get(storagePath, metadata.getRelativePath()).toString());
        }

        return record;
    }
    
    /**
     * 获取文件扩展名
     * 
     * @param fileName 文件名
     * @return 扩展名（不包含点号）
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }
    
    /**
     * 检测文件类型
     * 
     * @param fileName 文件名
     * @return MIME类型
     */
    private String detectFileType(String fileName) {
        String extension = getFileExtension(fileName);
        
        // 简化的MIME类型映射
        switch (extension) {
            case "txt": return "text/plain";
            case "pdf": return "application/pdf";
            case "jpg": case "jpeg": return "image/jpeg";
            case "png": return "image/png";
            case "gif": return "image/gif";
            case "mp4": return "video/mp4";
            case "mp3": return "audio/mpeg";
            case "zip": return "application/zip";
            case "doc": return "application/msword";
            case "docx": return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls": return "application/vnd.ms-excel";
            case "xlsx": return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            default: return "application/octet-stream";
        }
    }
} 