package com.sdesrd.filetransfer.server.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

import cn.hutool.crypto.digest.DigestUtil;

/**
 * 文件工具类
 */
public class FileUtils {
    
    /**
     * 计算字节数组的MD5
     */
    public static String calculateMD5(byte[] data) {
        return DigestUtil.md5Hex(data);
    }
    
    /**
     * 计算文件的MD5
     */
    public static String calculateFileMD5(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file)) {
            return DigestUtil.md5Hex(fis);
        }
    }
    
    /**
     * 复制文件
     */
    public static void copyFile(String sourcePath, String targetPath) throws IOException {
        Files.copy(Paths.get(sourcePath), Paths.get(targetPath), StandardCopyOption.REPLACE_EXISTING);
    }
    
    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long size) {
        if (size <= 0) {
            return "0 B";
        }

        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double fileSize = size;

        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }

        if (unitIndex == 0) {
            return String.format("%.0f %s", fileSize, units[unitIndex]);
        } else {
            // 如果是整数，显示一位小数；否则显示两位小数
            if (fileSize == Math.floor(fileSize)) {
                return String.format("%.1f %s", fileSize, units[unitIndex]);
            } else {
                return String.format("%.1f %s", fileSize, units[unitIndex]);
            }
        }
    }
} 