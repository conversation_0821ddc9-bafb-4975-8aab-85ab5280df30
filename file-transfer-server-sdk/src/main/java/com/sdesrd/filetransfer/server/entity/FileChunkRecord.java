package com.sdesrd.filetransfer.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 文件分块记录实体类
 * 适配SQLite数据库
 */
@Data
@TableName("file_chunk_record")
public class FileChunkRecord {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 文件传输记录ID
     */
    @TableField("transfer_id")
    private String transferId;
    
    /**
     * 文件标识符
     */
    @TableField("file_id")
    private String fileId;
    
    /**
     * 分块序号（从0开始）
     */
    @TableField("chunk_index")
    private Integer chunkIndex;
    
    /**
     * 分块大小（字节）
     */
    @TableField("chunk_size")
    private Long chunkSize;
    
    /**
     * 分块偏移量（字节）
     */
    @TableField("chunk_offset")
    private Long chunkOffset;
    
    /**
     * 分块存储路径
     */
    @TableField("chunk_path")
    private String chunkPath;
    
    /**
     * 分块MD5校验码
     */
    @TableField("chunk_md5")
    private String chunkMd5;
    
    /**
     * 传输状态：0-待传输，1-传输完成，2-传输失败
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 重试次数
     */
    @TableField("retry_count")
    private Integer retryCount;
    
    /**
     * 创建时间（ISO格式字符串）
     */
    @TableField("create_time")
    private String createTime;
    
    /**
     * 完成时间（ISO格式字符串）
     */
    @TableField("complete_time")
    private String completeTime;
    
    /**
     * 失败原因
     */
    @TableField("fail_reason")
    private String failReason;
} 