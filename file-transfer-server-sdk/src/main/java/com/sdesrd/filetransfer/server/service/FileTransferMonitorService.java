package com.sdesrd.filetransfer.server.service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.entity.FileTransferRecord;
import com.sdesrd.filetransfer.server.mapper.FileTransferRecordMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输监控服务
 */
@Slf4j
@Service
public class FileTransferMonitorService {
    
    @Autowired
    private FileTransferRecordMapper transferRecordMapper;
    
    @Autowired
    private FileTransferProperties properties;
    
    private ScheduledExecutorService scheduler;
    
    @PostConstruct
    public void init() {
        scheduler = Executors.newScheduledThreadPool(2);
        
        // 启动清理任务
        scheduler.scheduleAtFixedRate(this::cleanupExpiredRecords, 
                60, properties.getCleanupInterval() / 1000, TimeUnit.SECONDS);
        
        // 启动监控任务
        scheduler.scheduleAtFixedRate(this::monitorTransfers, 
                30, 300, TimeUnit.SECONDS); // 每5分钟监控一次
        
        log.info("文件传输监控服务启动完成");
    }
    
    @PreDestroy
    public void destroy() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("文件传输监控服务关闭完成");
    }
    
    /**
     * 清理过期的传输记录
     */
    private void cleanupExpiredRecords() {
        try {
            Instant expireTime = Instant.now().minus(properties.getRecordExpireTime(), ChronoUnit.MILLIS);
            String expireTimeStr = expireTime.toString();
            
            // 查询过期的传输记录
            QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
            query.lt("create_time", expireTimeStr)
                 .in("status", 2, 3); // 已完成或已失败的记录
            
            List<FileTransferRecord> expiredRecords = transferRecordMapper.selectList(query);
            
            if (!expiredRecords.isEmpty()) {
                log.info("开始清理过期传输记录，数量: {}", expiredRecords.size());
                
                for (FileTransferRecord record : expiredRecords) {
                    try {
                        // 删除数据库记录
                        transferRecordMapper.deleteById(record.getId());
                        
                        // TODO: 可以添加删除物理文件的逻辑
                        // deletePhysicalFile(record.getFilePath());
                        
                    } catch (Exception e) {
                        log.error("清理传输记录失败 - ID: {}", record.getId(), e);
                    }
                }
                
                log.info("清理过期传输记录完成，清理数量: {}", expiredRecords.size());
            }
            
        } catch (Exception e) {
            log.error("清理过期记录任务执行失败", e);
        }
    }
    
    /**
     * 监控传输状态
     */
    private void monitorTransfers() {
        try {
            // 统计各状态的传输数量
            long pendingCount = countByStatus(0);    // 待传输
            long transferringCount = countByStatus(1); // 传输中
            long completedCount = countByStatus(2);   // 已完成
            long failedCount = countByStatus(3);      // 已失败
            
            log.info("传输状态统计 - 待传输: {}, 传输中: {}, 已完成: {}, 已失败: {}", 
                    pendingCount, transferringCount, completedCount, failedCount);
            
            // 检查长时间无进展的传输
            checkStuckTransfers();
            
            // 检查失败率
            checkFailureRate(completedCount, failedCount);
            
        } catch (Exception e) {
            log.error("监控传输状态任务执行失败", e);
        }
    }
    
    /**
     * 检查卡住的传输
     */
    private void checkStuckTransfers() {
        Instant stuckTime = Instant.now().minus(2, ChronoUnit.HOURS); // 2小时无更新视为卡住
        String stuckTimeStr = stuckTime.toString();
        
        QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
        query.eq("status", 1) // 传输中
             .lt("update_time", stuckTimeStr);
        
        List<FileTransferRecord> stuckTransfers = transferRecordMapper.selectList(query);
        
        if (!stuckTransfers.isEmpty()) {
            log.warn("发现卡住的传输 - 数量: {}", stuckTransfers.size());
            
            for (FileTransferRecord record : stuckTransfers) {
                log.warn("卡住的传输 - ID: {}, 文件: {}, 最后更新: {}", 
                        record.getId(), record.getFileName(), record.getUpdateTime());
                
                // 可以选择将其标记为失败
                // markTransferAsFailed(record.getId(), "传输超时");
            }
        }
    }
    
    /**
     * 检查失败率
     */
    private void checkFailureRate(long completedCount, long failedCount) {
        long totalCount = completedCount + failedCount;
        if (totalCount > 0) {
            double failureRate = (double) failedCount / totalCount * 100;
            
            if (failureRate > 10) { // 失败率超过10%发出警告
                log.warn("传输失败率过高: {:.2f}% ({}/{})", failureRate, failedCount, totalCount);
            }
        }
    }
    
    /**
     * 按状态统计传输数量
     */
    private long countByStatus(int status) {
        QueryWrapper<FileTransferRecord> query = new QueryWrapper<>();
        query.eq("status", status);
        return transferRecordMapper.selectCount(query);
    }
    
    /**
     * 获取传输统计信息
     */
    public TransferStatistics getTransferStatistics() {
        TransferStatistics stats = new TransferStatistics();
        stats.setPendingCount(countByStatus(0));
        stats.setTransferringCount(countByStatus(1));
        stats.setCompletedCount(countByStatus(2));
        stats.setFailedCount(countByStatus(3));
        
        // 计算成功率
        long totalCount = stats.getCompletedCount() + stats.getFailedCount();
        if (totalCount > 0) {
            stats.setSuccessRate((double) stats.getCompletedCount() / totalCount * 100);
        }
        
        return stats;
    }
    
    /**
     * 传输统计信息
     */
    public static class TransferStatistics {
        private long pendingCount;
        private long transferringCount;
        private long completedCount;
        private long failedCount;
        private double successRate;
        
        // Getters and Setters
        public long getPendingCount() { return pendingCount; }
        public void setPendingCount(long pendingCount) { this.pendingCount = pendingCount; }
        
        public long getTransferringCount() { return transferringCount; }
        public void setTransferringCount(long transferringCount) { this.transferringCount = transferringCount; }
        
        public long getCompletedCount() { return completedCount; }
        public void setCompletedCount(long completedCount) { this.completedCount = completedCount; }
        
        public long getFailedCount() { return failedCount; }
        public void setFailedCount(long failedCount) { this.failedCount = failedCount; }
        
        public double getSuccessRate() { return successRate; }
        public void setSuccessRate(double successRate) { this.successRate = successRate; }
    }
} 