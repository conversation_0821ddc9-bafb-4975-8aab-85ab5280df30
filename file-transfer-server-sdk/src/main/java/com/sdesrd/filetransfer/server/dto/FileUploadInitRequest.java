package com.sdesrd.filetransfer.server.dto;

import lombok.Data;

/**
 * 文件上传初始化请求
 * 客户端指定fileId版本：支持客户端指定ULID格式的fileId
 */
@Data
public class FileUploadInitRequest {

    /**
     * 客户端指定的文件ID（ULID格式，必填）
     * 客户端必须提供符合ULID标准的26字符文件标识符
     * 服务端将验证格式有效性和唯一性
     */
    private String fileId;

    /**
     * 客户端原始文件名
     * 用户上传时的完整文件名，包含文件名和后缀（如：document.pdf、image.jpg等）
     * 如果不提供，系统将使用MD5值作为文件名
     */
    private String originalFileName;

    /**
     * 文件后缀名（不包含点号，如：jpg、pdf、txt等）
     * 可以为空字符串表示无后缀名文件
     * 如果提供了originalFileName，此字段可以从originalFileName中自动提取
     */
    private String fileExtension;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件MD5值
     */
    private String fileMd5;

    /**
     * 分块大小（字节）
     */
    private Long chunkSize;
}