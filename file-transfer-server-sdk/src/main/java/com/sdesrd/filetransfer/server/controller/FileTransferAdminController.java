package com.sdesrd.filetransfer.server.controller;

import java.io.File;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.dto.ApiResult;
import com.sdesrd.filetransfer.server.dto.SystemHealthResponse;
import com.sdesrd.filetransfer.server.interceptor.AuthInterceptor;
import com.sdesrd.filetransfer.server.service.FileTransferMonitorService;
import com.sdesrd.filetransfer.server.service.FileTransferMonitorService.TransferStatistics;
import com.sdesrd.filetransfer.server.service.FileTransferService;
import com.sdesrd.filetransfer.server.util.RateLimitUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输管理控制器
 * 提供系统监控和管理功能
 *
 * 注意：路径映射已移除"/filetransfer"前缀，现在通过服务端context-path统一配置
 * 最终API端点格式：/filetransfer/api/admin/* （通过context-path + @RequestMapping组合）
 */
@Slf4j
@RestController
@RequestMapping("/api/admin")
// @Api(tags = "文件传输管理接口") - Swagger注解已移除
public class FileTransferAdminController {

    @Autowired
    private FileTransferMonitorService monitorService;
    
    @Autowired
    private FileTransferService fileTransferService;

    @Autowired
    private FileTransferProperties properties;

    /**
     * 获取传输统计信息
     */
    @GetMapping("/statistics")
    // @ApiOperation("获取传输统计信息")
    public ApiResult<TransferStatistics> getStatistics(HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            log.debug("获取传输统计信息 - 用户: {}", currentUser);

            TransferStatistics statistics = monitorService.getTransferStatistics();
            return ApiResult.success(statistics);

        } catch (Exception e) {
            log.error("获取传输统计信息失败", e);
            return ApiResult.error("获取传输统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 系统健康检查（详细）
     */
    @GetMapping("/health")
    // @ApiOperation("系统健康检查（详细）")
    public ApiResult<SystemHealthResponse> systemHealth(HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            log.debug("系统健康检查 - 用户: {}", currentUser);

            // 构建详细的健康检查信息
            SystemHealthResponse healthResponse = new SystemHealthResponse();
            healthResponse.setStatus("UP");
            healthResponse.setTimestamp(System.currentTimeMillis());

            // 获取JVM内存信息
            Runtime runtime = Runtime.getRuntime();
            healthResponse.setTotalMemory(runtime.totalMemory());
            healthResponse.setFreeMemory(runtime.freeMemory());
            healthResponse.setUsedMemory(runtime.totalMemory() - runtime.freeMemory());
            healthResponse.setMaxMemory(runtime.maxMemory());

            // 获取磁盘空间信息
            getDiskSpaceInfo(healthResponse);

            // 获取传输统计信息
            TransferStatistics transferStats = monitorService.getTransferStatistics();
            healthResponse.setTransferStats(transferStats);

            return ApiResult.success(healthResponse);

        } catch (Exception e) {
            log.error("系统健康检查失败", e);
            return ApiResult.error("系统健康检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取磁盘空间信息
     * 
     * @param healthResponse 健康检查响应对象
     */
    private void getDiskSpaceInfo(SystemHealthResponse healthResponse) {
        try {
            // 优先使用数据库路径，如果不可用则使用默认存储路径
            String checkPath = null;
            
            // 尝试使用数据库路径
            if (properties.getDatabasePath() != null) {
                File dbFile = new File(properties.getDatabasePath());
                if (dbFile.getParentFile() != null && dbFile.getParentFile().exists()) {
                    checkPath = dbFile.getParent();
                }
            }
            
            // 如果数据库路径不可用，使用默认存储路径
            if (checkPath == null && properties.getDefaultConfig() != null 
                && properties.getDefaultConfig().getStoragePath() != null) {
                File storageFile = new File(properties.getDefaultConfig().getStoragePath());
                if (storageFile.exists() || storageFile.getParentFile() != null) {
                    checkPath = storageFile.exists() ? storageFile.getAbsolutePath() : storageFile.getParent();
                }
            }
            
            // 如果以上路径都不可用，使用当前工作目录
            if (checkPath == null) {
                checkPath = System.getProperty("user.dir", ".");
            }
            
            File diskFile = new File(checkPath);
            
            // 获取磁盘空间信息
            long totalSpace = diskFile.getTotalSpace();
            long freeSpace = diskFile.getFreeSpace();
            long usableSpace = diskFile.getUsableSpace();
            
            healthResponse.setTotalSpace(totalSpace);
            healthResponse.setFreeSpace(freeSpace);
            healthResponse.setUsableSpace(usableSpace);
            
            log.debug("磁盘空间信息获取成功 - 检查路径: {}, 总空间: {}GB, 空闲空间: {}GB, 可用空间: {}GB", 
                    checkPath,
                    totalSpace / (1024.0 * 1024.0 * 1024.0),
                    freeSpace / (1024.0 * 1024.0 * 1024.0),
                    usableSpace / (1024.0 * 1024.0 * 1024.0));
            
        } catch (Exception e) {
            log.warn("获取磁盘空间信息失败: {}", e.getMessage());
            // 设置默认值或null，避免影响整个健康检查
            healthResponse.setTotalSpace(null);
            healthResponse.setFreeSpace(null);
            healthResponse.setUsableSpace(null);
        }
    }

    /**
     * 清理限流器缓存
     */
    @GetMapping("/clear-rate-limiters")
    // @ApiOperation("清理限流器缓存")
    public ApiResult<String> clearRateLimiters(HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            log.info("清理限流器缓存 - 用户: {}", currentUser);

            // 清理所有限流器
            RateLimitUtils.clearAllRateLimiters();

            log.info("限流器缓存清理完成 - 操作用户: {}", currentUser);
            return ApiResult.success("限流器缓存清理完成");

        } catch (Exception e) {
            log.error("清理限流器缓存失败", e);
            return ApiResult.error("清理限流器缓存失败: " + e.getMessage());
        }
    }
    
    // 注意：数据库重建端点已移至DatabaseManagementController统一管理
    // 避免功能重复，所有数据库管理相关操作请使用 /filetransfer/api/database/ 路径下的接口
}