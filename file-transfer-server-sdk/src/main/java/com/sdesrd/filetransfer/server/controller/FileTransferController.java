package com.sdesrd.filetransfer.server.controller;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.sdesrd.filetransfer.server.dto.ApiResult;
import com.sdesrd.filetransfer.server.dto.FileInfo;
import com.sdesrd.filetransfer.server.dto.FileUploadCompleteResponse;
import com.sdesrd.filetransfer.server.dto.FileUploadInitRequest;
import com.sdesrd.filetransfer.server.dto.FileUploadInitResponse;
import com.sdesrd.filetransfer.server.dto.TransferProgressResponse;
import com.sdesrd.filetransfer.server.interceptor.AuthInterceptor;
import com.sdesrd.filetransfer.server.service.FileTransferService;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输控制器
 *
 * 注意：路径映射已移除"/filetransfer"前缀，现在通过服务端context-path统一配置
 * 最终API端点格式：/filetransfer/api/file/* （通过context-path + @RequestMapping组合）
 */
@Slf4j
@RestController
@RequestMapping("/api/file")
// @Api(tags = "文件传输接口") - Swagger注解已移除
public class FileTransferController {
    
    @Autowired
    private FileTransferService fileTransferService;
    
    /**
     * 初始化文件上传
     */
    @PostMapping("/upload/init")
    // @ApiOperation("初始化文件上传")
    public ApiResult<FileUploadInitResponse> initUpload(
            @RequestBody FileUploadInitRequest request,
            HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);
            
            log.info("初始化上传 - 用户: {}, 文件后缀: {}, 大小: {}, MD5: {}, IP: {}",
                    currentUser, request.getFileExtension(), request.getFileSize(), request.getFileMd5(), clientIp);
            
            FileUploadInitResponse response = fileTransferService.initUpload(request, currentUser, clientIp);
            return ApiResult.success(response);
            
        } catch (Exception e) {
            log.error("初始化上传失败", e);
            return ApiResult.error("初始化上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 上传文件分块
     */
    @PostMapping("/upload/chunk")
    // @ApiOperation("上传文件分块")
    public ApiResult<String> uploadChunk(
            @RequestParam("transferId") /* @ApiParam("传输ID") */ String transferId,
            @RequestParam("chunkIndex") /* @ApiParam("分块序号") */ Integer chunkIndex,
            @RequestParam("chunkMd5") /* @ApiParam("分块MD5") */ String chunkMd5,
            @RequestParam("chunk") /* @ApiParam("分块数据") */ MultipartFile chunkFile,
            HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);
            
            log.debug("上传分块 - 用户: {}, 传输ID: {}, 分块: {}, 大小: {}, IP: {}", 
                    currentUser, transferId, chunkIndex, chunkFile.getSize(), clientIp);
            
            fileTransferService.uploadChunk(transferId, chunkIndex, chunkMd5, chunkFile, currentUser);
            return ApiResult.success("分块上传成功");
            
        } catch (Exception e) {
            log.error("上传分块失败 - 传输ID: {}, 分块: {}", transferId, chunkIndex, e);
            return ApiResult.error("上传分块失败: " + e.getMessage());
        }
    }
    
    /**
     * 完成文件上传
     * 重构后的版本：返回完整的上传结果信息，包括fileId和相对路径
     */
    @PostMapping("/upload/complete/{transferId}")
    // @ApiOperation("完成文件上传")
    public ApiResult<FileUploadCompleteResponse> completeUpload(
            @PathVariable("transferId") /* @ApiParam("传输ID") */ String transferId,
            HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);

            log.info("完成上传 - 用户: {}, 传输ID: {}, IP: {}", currentUser, transferId, clientIp);

            FileUploadCompleteResponse response = fileTransferService.completeUpload(transferId, currentUser);
            return ApiResult.success("文件上传完成", response);

        } catch (Exception e) {
            log.error("完成上传失败 - 传输ID: {}", transferId, e);
            return ApiResult.error("完成上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 下载文件
     */
    @GetMapping("/download/{fileId}")
    // @ApiOperation("下载文件")
    public void downloadFile(
            @PathVariable("fileId") /* @ApiParam("文件ID") */ String fileId,
            HttpServletRequest httpRequest,
            HttpServletResponse httpResponse) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);
            
            log.info("下载文件 - 用户: {}, 文件ID: {}, IP: {}", currentUser, fileId, clientIp);
            
            fileTransferService.downloadFile(fileId, currentUser, httpResponse);
            
        } catch (Exception e) {
            log.error("下载文件失败 - 文件ID: {}", fileId, e);
            try {
                httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                httpResponse.setContentType("application/json;charset=UTF-8");
                httpResponse.getWriter().write("{\"code\":500,\"message\":\"下载文件失败: " + e.getMessage() + "\",\"data\":null}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }
    
    /**
     * 查询传输进度
     */
    @GetMapping("/progress/{transferId}")
    // @ApiOperation("查询传输进度")
    public ApiResult<TransferProgressResponse> queryProgress(
            @PathVariable("transferId") /* @ApiParam("传输ID") */ String transferId,
            HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            
            log.debug("查询进度 - 用户: {}, 传输ID: {}", currentUser, transferId);
            
            TransferProgressResponse progress = fileTransferService.queryProgress(transferId, currentUser);
            return ApiResult.success(progress);
            
        } catch (Exception e) {
            log.error("查询进度失败 - 传输ID: {}", transferId, e);
            return ApiResult.error("查询进度失败: " + e.getMessage());
        }
    }
    
    /**
     * 健康检查（无需认证）
     */
    @GetMapping("/health")
    // @ApiOperation("健康检查")
    public ApiResult<String> health() {
        return ApiResult.success("服务正常", "OK");
    }
    
    /**
     * 获取文件下载信息
     */
    @GetMapping("/download/info/{fileId}")
    // @ApiOperation("获取文件下载信息")
    public ApiResult<FileInfo> getFileInfo(
            @PathVariable("fileId") /* @ApiParam("文件ID") */ String fileId,
            HttpServletRequest httpRequest) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);
            
            log.info("获取文件信息 - 用户: {}, 文件ID: {}, IP: {}", currentUser, fileId, clientIp);
            
            FileInfo fileInfo = fileTransferService.getFileInfo(fileId, currentUser);
            return ApiResult.success(fileInfo);
            
        } catch (Exception e) {
            log.error("获取文件信息失败 - 文件ID: {}", fileId, e);
            return ApiResult.error("获取文件信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 分块下载文件
     * 支持HTTP Range请求和断点续传
     */
    @GetMapping("/download/chunk/{fileId}")
    // @ApiOperation("分块下载文件")
    public void downloadFileChunk(
            @PathVariable("fileId") /* @ApiParam("文件ID") */ String fileId,
            HttpServletRequest httpRequest,
            HttpServletResponse httpResponse) {
        try {
            String currentUser = AuthInterceptor.getCurrentUser(httpRequest);
            String clientIp = getClientIp(httpRequest);
            
            String rangeHeader = httpRequest.getHeader("Range");
            log.info("分块下载文件 - 用户: {}, 文件ID: {}, Range: {}, IP: {}", 
                    currentUser, fileId, rangeHeader, clientIp);
            
            fileTransferService.downloadFileChunk(fileId, currentUser, httpRequest, httpResponse);
            
        } catch (Exception e) {
            log.error("分块下载文件失败 - 文件ID: {}", fileId, e);
            try {
                httpResponse.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                httpResponse.setContentType("application/json;charset=UTF-8");
                httpResponse.getWriter().write("{\"code\":500,\"message\":\"分块下载文件失败: " + e.getMessage() + "\",\"data\":null}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }
    

    
    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
} 