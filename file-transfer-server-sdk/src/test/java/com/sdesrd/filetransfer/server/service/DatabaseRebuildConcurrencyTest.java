package com.sdesrd.filetransfer.server.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.config.UserConfig;
import com.sdesrd.filetransfer.server.mapper.FileTransferRecordMapper;
import com.sdesrd.filetransfer.server.service.DatabaseManagementService.DatabaseRebuildResult;

/**
 * 数据库重建并发控制测试
 * 验证存储路径冲突和并发重建请求的处理
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("数据库重建并发控制测试")
class DatabaseRebuildConcurrencyTest {

    @Mock
    private FileTransferProperties properties;
    
    @Mock
    private FileTransferRecordMapper transferRecordMapper;
    
    @Mock
    private DatabaseFallbackService databaseFallbackService;
    
    @InjectMocks
    private DatabaseManagementService databaseManagementService;
    
    private UserConfig defaultConfig;
    private Map<String, UserConfig> users;
    
    @BeforeEach
    void setUp() {
        // 设置默认配置
        defaultConfig = new UserConfig();
        defaultConfig.setStoragePath("./data/file-transfer/files");
        
        // 设置用户配置 - 包含路径冲突场景
        users = new HashMap<>();
        
        // demo用户 - 独立路径
        UserConfig demoConfig = new UserConfig();
        demoConfig.setStoragePath("./data/demo/files");
        users.put("demo", demoConfig);
        
        // test用户 - 与另一个用户共享路径
        UserConfig testConfig = new UserConfig();
        testConfig.setStoragePath("./data/shared/files");
        users.put("test", testConfig);
        
        // user1用户 - 与test用户共享相同路径
        UserConfig user1Config = new UserConfig();
        user1Config.setStoragePath("./data/shared/files"); // 相同路径
        users.put("user1", user1Config);
        
        // user2用户 - 使用默认路径
        UserConfig user2Config = new UserConfig();
        user2Config.setStoragePath("./data/file-transfer/files"); // 与默认路径相同
        users.put("user2", user2Config);
        
        // admin用户 - 独立路径
        UserConfig adminConfig = new UserConfig();
        adminConfig.setStoragePath("./data/admin/files");
        users.put("admin", adminConfig);
        
        // 模拟配置属性
        when(properties.getDefaultConfig()).thenReturn(defaultConfig);
        when(properties.getUsers()).thenReturn(users);
    }
    
    @Test
    @DisplayName("验证存储路径去重逻辑")
    void testStoragePathDeduplication() {
        // 模拟DatabaseFallbackService的扫描结果
        Map<String, Object> mockScanResult = new HashMap<>();
        mockScanResult.put("success", true);
        mockScanResult.put("scannedFiles", 100);
        mockScanResult.put("rebuiltRecords", 80);
        mockScanResult.put("skippedFiles", 20);
        mockScanResult.put("errors", new java.util.ArrayList<>());
        
        // 模拟用户扫描结果 - 应该只有3个唯一路径被扫描
        Map<String, Object> userResults = new HashMap<>();
        
        // 默认路径结果
        Map<String, Object> defaultResult = new HashMap<>();
        defaultResult.put("scannedFiles", 30);
        defaultResult.put("rebuiltRecords", 25);
        defaultResult.put("skippedFiles", 5);
        userResults.put("default", defaultResult);
        
        // 共享路径结果（test和user1共享）
        Map<String, Object> sharedResult = new HashMap<>();
        sharedResult.put("scannedFiles", 40);
        sharedResult.put("rebuiltRecords", 30);
        sharedResult.put("skippedFiles", 10);
        userResults.put("./data/shared/files", sharedResult);
        
        // demo用户独立路径结果
        Map<String, Object> demoResult = new HashMap<>();
        demoResult.put("scannedFiles", 20);
        demoResult.put("rebuiltRecords", 15);
        demoResult.put("skippedFiles", 5);
        userResults.put("./data/demo/files", demoResult);
        
        // admin用户独立路径结果
        Map<String, Object> adminResult = new HashMap<>();
        adminResult.put("scannedFiles", 10);
        adminResult.put("rebuiltRecords", 10);
        adminResult.put("skippedFiles", 0);
        userResults.put("./data/admin/files", adminResult);
        
        mockScanResult.put("userResults", userResults);
        
        when(databaseFallbackService.scanAndRebuildFromMetadata()).thenReturn(mockScanResult);
        
        // 执行重建
        DatabaseRebuildResult result = databaseManagementService.rebuildDatabaseFromDisk("admin", "127.0.0.1");
        
        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(100, result.getScannedFiles());
        assertEquals(80, result.getRebuiltRecords());
        assertEquals(20, result.getFailedFiles());
        
        // 验证DatabaseFallbackService只被调用一次
        verify(databaseFallbackService, times(1)).scanAndRebuildFromMetadata();
        
        // 验证消息包含路径去重信息
        assertNotNull(result.getMessage());
        assertTrue(result.getMessage().contains("多用户扫描重建完成"));
    }
    
    @Test
    @DisplayName("验证并发重建请求控制")
    void testConcurrentRebuildControl() throws InterruptedException {
        // 模拟一个长时间运行的扫描操作
        when(databaseFallbackService.scanAndRebuildFromMetadata()).thenAnswer(invocation -> {
            try {
                Thread.sleep(1000); // 模拟1秒的扫描时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("scannedFiles", 50);
            result.put("rebuiltRecords", 40);
            result.put("skippedFiles", 10);
            result.put("errors", new java.util.ArrayList<>());
            result.put("userResults", new HashMap<>());
            return result;
        });
        
        ExecutorService executor = Executors.newFixedThreadPool(3);
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(3);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger rejectedCount = new AtomicInteger(0);
        
        // 启动3个并发重建请求
        for (int i = 0; i < 3; i++) {
            final int requestId = i;
            executor.submit(() -> {
                try {
                    startLatch.await(); // 等待同时开始
                    
                    DatabaseRebuildResult result = databaseManagementService.rebuildDatabaseFromDisk(
                        "admin" + requestId, "127.0.0." + (1 + requestId));
                    
                    if (result.isSuccess()) {
                        successCount.incrementAndGet();
                    } else {
                        rejectedCount.incrementAndGet();
                        assertTrue(result.getErrorMessage().contains("数据库重建操作已在进行中"));
                    }
                    
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    finishLatch.countDown();
                }
            });
        }
        
        // 同时启动所有请求
        startLatch.countDown();
        
        // 等待所有请求完成
        assertTrue(finishLatch.await(5, TimeUnit.SECONDS));
        
        // 验证结果：只有一个成功，其他被拒绝
        assertEquals(1, successCount.get(), "应该只有一个重建请求成功");
        assertEquals(2, rejectedCount.get(), "应该有两个重建请求被拒绝");
        
        // 验证DatabaseFallbackService只被调用一次
        verify(databaseFallbackService, times(1)).scanAndRebuildFromMetadata();
        
        executor.shutdown();
    }
    
    @Test
    @DisplayName("验证重建状态查询")
    void testRebuildStatusQuery() {
        // 初始状态：无重建操作
        Map<String, Object> status = databaseManagementService.getRebuildStatus();
        assertFalse((Boolean) status.get("inProgress"));
        
        // 模拟长时间运行的重建操作
        when(databaseFallbackService.scanAndRebuildFromMetadata()).thenAnswer(invocation -> {
            // 在扫描过程中检查状态
            Map<String, Object> currentStatus = databaseManagementService.getRebuildStatus();
            assertTrue((Boolean) currentStatus.get("inProgress"));
            assertNotNull(currentStatus.get("operationId"));
            assertNotNull(currentStatus.get("startTime"));
            assertEquals("testUser", currentStatus.get("initiatorUser"));
            assertEquals("*************", currentStatus.get("initiatorIp"));
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("scannedFiles", 10);
            result.put("rebuiltRecords", 8);
            result.put("skippedFiles", 2);
            result.put("errors", new java.util.ArrayList<>());
            result.put("userResults", new HashMap<>());
            return result;
        });
        
        // 执行重建
        DatabaseRebuildResult result = databaseManagementService.rebuildDatabaseFromDisk("testUser", "*************");
        
        // 验证重建完成后状态
        assertTrue(result.isSuccess());
        
        Map<String, Object> finalStatus = databaseManagementService.getRebuildStatus();
        assertFalse((Boolean) finalStatus.get("inProgress"));
    }
}
