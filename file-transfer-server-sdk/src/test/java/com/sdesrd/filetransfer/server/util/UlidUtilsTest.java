package com.sdesrd.filetransfer.server.util;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ULID工具类测试
 */
@DisplayName("ULID工具类测试")
class UlidUtilsTest {
    
    /**
     * 时间戳常量定义
     */
    private static final long TEST_TIMESTAMP = 1703980800000L; // 2023-12-31 00:00:00 UTC
    private static final String EXPECTED_YEAR_MONTH = "202312";
    
    @Test
    @DisplayName("生成ULID测试")
    void testGenerateUlid() {
        // 生成ULID
        String ulid = UlidUtils.generateUlid();
        
        // 验证ULID不为空
        assertNotNull(ulid, "生成的ULID不应为空");
        
        // 验证ULID格式
        assertTrue(UlidUtils.isValidUlid(ulid), "生成的ULID格式应该有效");
        
        // 验证ULID长度
        assertEquals(26, ulid.length(), "ULID长度应为26个字符");
        
        // 验证ULID只包含有效字符
        assertTrue(ulid.matches("^[0-9A-HJKMNP-TV-Z]{26}$"), "ULID应只包含Crockford's Base32字符");
    }
    
    @Test
    @DisplayName("生成指定时间戳的ULID测试")
    void testGenerateUlidWithTimestamp() {
        // 生成指定时间戳的ULID
        String ulid = UlidUtils.generateUlid(TEST_TIMESTAMP);
        
        // 验证ULID有效性
        assertTrue(UlidUtils.isValidUlid(ulid), "生成的ULID应该有效");
        
        // 验证时间戳提取
        Long extractedTimestamp = UlidUtils.extractTimestamp(ulid);
        assertNotNull(extractedTimestamp, "应能从ULID中提取时间戳");
        assertEquals(TEST_TIMESTAMP, extractedTimestamp.longValue(), "提取的时间戳应与原始时间戳一致");
    }
    
    @Test
    @DisplayName("ULID格式验证测试")
    void testIsValidUlid() {
        // 测试有效的ULID
        String validUlid = UlidUtils.generateUlid();
        assertTrue(UlidUtils.isValidUlid(validUlid), "生成的ULID应该有效");
        
        // 测试无效的ULID
        assertFalse(UlidUtils.isValidUlid(null), "null应该无效");
        assertFalse(UlidUtils.isValidUlid(""), "空字符串应该无效");
        assertFalse(UlidUtils.isValidUlid("   "), "空白字符串应该无效");
        assertFalse(UlidUtils.isValidUlid("123"), "长度不足的字符串应该无效");
        assertFalse(UlidUtils.isValidUlid("0123456789012345678901234"), "长度不足的字符串应该无效（25字符）");
        assertFalse(UlidUtils.isValidUlid("012345678901234567890123456"), "长度过长的字符串应该无效（27字符）");
        assertFalse(UlidUtils.isValidUlid("01234567890123456789012345I"), "包含无效字符I的字符串应该无效");
        assertFalse(UlidUtils.isValidUlid("01234567890123456789012345L"), "包含无效字符L的字符串应该无效");
        assertFalse(UlidUtils.isValidUlid("01234567890123456789012345O"), "包含无效字符O的字符串应该无效");
        assertFalse(UlidUtils.isValidUlid("01234567890123456789012345U"), "包含无效字符U的字符串应该无效");
    }
    
    @Test
    @DisplayName("从ULID提取时间戳测试")
    void testExtractTimestamp() {
        // 使用已知时间戳生成ULID
        String ulid = UlidUtils.generateUlid(TEST_TIMESTAMP);
        
        // 提取时间戳
        Long extractedTimestamp = UlidUtils.extractTimestamp(ulid);
        
        // 验证结果
        assertNotNull(extractedTimestamp, "应能成功提取时间戳");
        assertEquals(TEST_TIMESTAMP, extractedTimestamp.longValue(), "提取的时间戳应与原始时间戳一致");
        
        // 测试无效ULID
        assertNull(UlidUtils.extractTimestamp(null), "无效ULID应返回null");
        assertNull(UlidUtils.extractTimestamp("invalid"), "无效ULID应返回null");
    }
    
    @Test
    @DisplayName("从ULID提取年月信息测试")
    void testExtractYearMonth() {
        // 使用已知时间戳生成ULID
        String ulid = UlidUtils.generateUlid(TEST_TIMESTAMP);
        
        // 提取年月信息
        String yearMonth = UlidUtils.extractYearMonth(ulid);
        
        // 验证结果
        assertNotNull(yearMonth, "应能成功提取年月信息");
        assertEquals(EXPECTED_YEAR_MONTH, yearMonth, "提取的年月信息应正确");
        
        // 测试无效ULID
        assertNull(UlidUtils.extractYearMonth(null), "无效ULID应返回null");
        assertNull(UlidUtils.extractYearMonth("invalid"), "无效ULID应返回null");
    }
    
    @Test
    @DisplayName("从ULID提取Instant对象测试")
    void testExtractInstant() {
        // 使用已知时间戳生成ULID
        String ulid = UlidUtils.generateUlid(TEST_TIMESTAMP);
        
        // 提取Instant对象
        Instant instant = UlidUtils.extractInstant(ulid);
        
        // 验证结果
        assertNotNull(instant, "应能成功提取Instant对象");
        assertEquals(TEST_TIMESTAMP, instant.toEpochMilli(), "提取的Instant时间戳应正确");
        
        // 测试无效ULID
        assertNull(UlidUtils.extractInstant(null), "无效ULID应返回null");
        assertNull(UlidUtils.extractInstant("invalid"), "无效ULID应返回null");
    }
    
    @Test
    @DisplayName("从ULID提取LocalDateTime对象测试")
    void testExtractLocalDateTime() {
        // 使用已知时间戳生成ULID
        String ulid = UlidUtils.generateUlid(TEST_TIMESTAMP);
        
        // 提取LocalDateTime对象
        LocalDateTime dateTime = UlidUtils.extractLocalDateTime(ulid);
        
        // 验证结果
        assertNotNull(dateTime, "应能成功提取LocalDateTime对象");
        
        // 验证时间戳一致性
        Instant expectedInstant = Instant.ofEpochMilli(TEST_TIMESTAMP);
        LocalDateTime expectedDateTime = LocalDateTime.ofInstant(expectedInstant, ZoneId.systemDefault());
        assertEquals(expectedDateTime, dateTime, "提取的LocalDateTime应正确");
        
        // 测试无效ULID
        assertNull(UlidUtils.extractLocalDateTime(null), "无效ULID应返回null");
        assertNull(UlidUtils.extractLocalDateTime("invalid"), "无效ULID应返回null");
    }
    
    @Test
    @DisplayName("检查ULID时间范围测试")
    void testIsInTimeRange() {
        // 生成测试ULID
        String ulid = UlidUtils.generateUlid(TEST_TIMESTAMP);
        
        // 测试在范围内
        long startTime = TEST_TIMESTAMP - 1000;
        long endTime = TEST_TIMESTAMP + 1000;
        assertTrue(UlidUtils.isInTimeRange(ulid, startTime, endTime), "ULID应在指定时间范围内");
        
        // 测试不在范围内
        long futureStart = TEST_TIMESTAMP + 1000;
        long futureEnd = TEST_TIMESTAMP + 2000;
        assertFalse(UlidUtils.isInTimeRange(ulid, futureStart, futureEnd), "ULID不应在未来时间范围内");
        
        // 测试无效ULID
        assertFalse(UlidUtils.isInTimeRange("invalid", startTime, endTime), "无效ULID应返回false");
    }
    
    @Test
    @DisplayName("比较ULID时间戳测试")
    void testCompareTimestamp() {
        // 生成两个不同时间戳的ULID
        String ulid1 = UlidUtils.generateUlid(TEST_TIMESTAMP);
        String ulid2 = UlidUtils.generateUlid(TEST_TIMESTAMP + 1000);
        
        // 比较时间戳
        Integer result = UlidUtils.compareTimestamp(ulid1, ulid2);
        assertNotNull(result, "比较结果不应为null");
        assertTrue(result < 0, "ulid1应早于ulid2");
        
        // 测试相同时间戳
        String ulid3 = UlidUtils.generateUlid(TEST_TIMESTAMP);
        Integer sameResult = UlidUtils.compareTimestamp(ulid1, ulid3);
        assertNotNull(sameResult, "比较结果不应为null");
        assertEquals(0, sameResult.intValue(), "相同时间戳的ULID比较结果应为0");
        
        // 测试无效ULID
        assertNull(UlidUtils.compareTimestamp("invalid", ulid1), "无效ULID比较应返回null");
        assertNull(UlidUtils.compareTimestamp(ulid1, "invalid"), "无效ULID比较应返回null");
    }
    
    @Test
    @DisplayName("格式化ULID信息测试")
    void testFormatUlidInfo() {
        // 生成测试ULID
        String ulid = UlidUtils.generateUlid(TEST_TIMESTAMP);
        
        // 格式化信息
        String info = UlidUtils.formatUlidInfo(ulid);
        
        // 验证结果
        assertNotNull(info, "格式化信息不应为null");
        assertTrue(info.contains(ulid), "格式化信息应包含ULID");
        assertTrue(info.contains(String.valueOf(TEST_TIMESTAMP)), "格式化信息应包含时间戳");
        assertTrue(info.contains(EXPECTED_YEAR_MONTH), "格式化信息应包含年月");
        
        // 测试无效ULID
        String invalidInfo = UlidUtils.formatUlidInfo("invalid");
        assertTrue(invalidInfo.contains("无效的ULID"), "无效ULID应返回错误信息");
    }
    
    @Test
    @DisplayName("ULID唯一性测试")
    void testUlidUniqueness() {
        // 生成多个ULID
        String ulid1 = UlidUtils.generateUlid();
        String ulid2 = UlidUtils.generateUlid();
        String ulid3 = UlidUtils.generateUlid();
        
        // 验证唯一性
        assertNotEquals(ulid1, ulid2, "生成的ULID应该唯一");
        assertNotEquals(ulid1, ulid3, "生成的ULID应该唯一");
        assertNotEquals(ulid2, ulid3, "生成的ULID应该唯一");
    }
    
    @Test
    @DisplayName("ULID时间顺序测试")
    void testUlidTimeOrder() throws InterruptedException {
        // 生成第一个ULID
        String ulid1 = UlidUtils.generateUlid();
        
        // 等待一小段时间
        Thread.sleep(10);
        
        // 生成第二个ULID
        String ulid2 = UlidUtils.generateUlid();
        
        // 比较时间戳
        Integer comparison = UlidUtils.compareTimestamp(ulid1, ulid2);
        assertNotNull(comparison, "比较结果不应为null");
        assertTrue(comparison <= 0, "后生成的ULID时间戳应大于或等于先生成的");
    }
}
