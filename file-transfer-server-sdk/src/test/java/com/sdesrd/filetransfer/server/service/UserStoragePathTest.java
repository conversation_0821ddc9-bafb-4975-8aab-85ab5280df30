package com.sdesrd.filetransfer.server.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.io.File;
import java.nio.file.Paths;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.sdesrd.filetransfer.server.config.FileTransferProperties;
import com.sdesrd.filetransfer.server.config.UserConfig;

/**
 * 用户存储路径配置测试
 * 验证用户级别的 storage-path 配置是否正确生效
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("用户存储路径配置测试")
class UserStoragePathTest {

    @Mock
    private FileTransferProperties properties;
    
    @Mock
    private AuthService authService;
    
    @InjectMocks
    private FileTransferService fileTransferService;
    
    private UserConfig demoUserConfig;
    private UserConfig defaultConfig;
    
    @BeforeEach
    void setUp() {
        // 设置默认配置
        defaultConfig = new UserConfig();
        defaultConfig.setStoragePath("./data/file-transfer/files");
        defaultConfig.setMaxFileSize(104857600L); // 100MB
        defaultConfig.setDefaultChunkSize(2097152L); // 2MB
        defaultConfig.setFastUploadEnabled(true);
        defaultConfig.setRateLimitEnabled(true);
        
        // 设置demo用户配置
        demoUserConfig = new UserConfig();
        demoUserConfig.setSecretKey("demo-secret-key-2024");
        demoUserConfig.setRole(UserConfig.Role.USER);
        demoUserConfig.setStoragePath("./data/demo/files"); // 用户专用存储路径
        demoUserConfig.setMaxFileSize(104857600L);
        demoUserConfig.setDefaultChunkSize(1048576L); // 1MB
        demoUserConfig.setFastUploadEnabled(true);
        demoUserConfig.setRateLimitEnabled(true);
        
        // 模拟配置属性
        when(properties.getDefaultConfig()).thenReturn(defaultConfig);
        when(properties.getUserConfig("demo")).thenReturn(demoUserConfig);
        when(properties.getUserConfig("nonexistent-user")).thenReturn(defaultConfig);
        
        // 模拟认证服务
        when(authService.getUserConfig("demo")).thenReturn(demoUserConfig);
        when(authService.getUserConfig("nonexistent-user")).thenReturn(defaultConfig);
    }
    
    @Test
    @DisplayName("验证demo用户获取正确的存储路径配置")
    void testDemoUserStoragePath() {
        // 获取demo用户配置
        UserConfig config = authService.getUserConfig("demo");
        
        // 验证存储路径
        assertNotNull(config);
        assertEquals("./data/demo/files", config.getStoragePath());
        assertNotEquals(defaultConfig.getStoragePath(), config.getStoragePath());
        
        // 验证其他配置
        assertEquals("demo-secret-key-2024", config.getSecretKey());
        assertEquals(UserConfig.Role.USER, config.getRole());
        assertEquals(1048576L, config.getDefaultChunkSize());
    }
    
    @Test
    @DisplayName("验证不存在的用户使用默认存储路径")
    void testNonexistentUserUsesDefaultStoragePath() {
        // 获取不存在用户的配置
        UserConfig config = authService.getUserConfig("nonexistent-user");
        
        // 验证使用默认存储路径
        assertNotNull(config);
        assertEquals("./data/file-transfer/files", config.getStoragePath());
        assertEquals(defaultConfig.getStoragePath(), config.getStoragePath());
        
        // 验证没有密钥（默认配置特征）
        assertNull(config.getSecretKey());
    }
    
    @Test
    @DisplayName("验证文件路径构建使用用户配置的存储路径")
    void testFilePathBuildingWithUserStoragePath() {
        // 模拟文件ID和文件名
        String fileId = "01HN8X9K2M3P4Q5R6S7T8U9V0W"; // 示例ULID
        String fileName = "test.txt";
        String username = "demo";
        
        // 预期的文件路径应该基于demo用户的存储路径
        String expectedBasePath = "./data/demo/files";
        
        // 验证用户配置确实返回了正确的存储路径
        UserConfig userConfig = authService.getUserConfig(username);
        assertEquals(expectedBasePath, userConfig.getStoragePath());
        
        // 验证路径构建逻辑（这里我们验证配置是否正确，实际的路径构建在私有方法中）
        String yearMonth = "202401"; // 假设的年月
        String expectedPath = Paths.get(expectedBasePath, yearMonth, fileId, fileName).toString();
        
        // 验证路径包含用户专用的存储目录
        assertTrue(expectedPath.contains("data/demo/files"));
        assertFalse(expectedPath.contains("data/file-transfer/files"));
    }
    
    @Test
    @DisplayName("验证用户配置合并逻辑")
    void testUserConfigMerging() {
        // 创建部分配置的用户
        UserConfig partialConfig = new UserConfig();
        partialConfig.setSecretKey("partial-key");
        partialConfig.setStoragePath("./data/partial/files");
        // 其他配置项为null，应该使用默认值
        
        // 模拟配置合并（这里模拟FileTransferProperties.mergeWithDefault的行为）
        UserConfig mergedConfig = new UserConfig();
        mergedConfig.setSecretKey(partialConfig.getSecretKey());
        mergedConfig.setStoragePath(partialConfig.getStoragePath() != null ? 
                                   partialConfig.getStoragePath() : defaultConfig.getStoragePath());
        mergedConfig.setMaxFileSize(partialConfig.getMaxFileSize() != null ? 
                                   partialConfig.getMaxFileSize() : defaultConfig.getMaxFileSize());
        mergedConfig.setDefaultChunkSize(partialConfig.getDefaultChunkSize() != null ? 
                                        partialConfig.getDefaultChunkSize() : defaultConfig.getDefaultChunkSize());
        
        // 验证合并结果
        assertEquals("partial-key", mergedConfig.getSecretKey());
        assertEquals("./data/partial/files", mergedConfig.getStoragePath());
        assertEquals(defaultConfig.getMaxFileSize(), mergedConfig.getMaxFileSize());
        assertEquals(defaultConfig.getDefaultChunkSize(), mergedConfig.getDefaultChunkSize());
    }
    
    @Test
    @DisplayName("验证存储目录创建逻辑")
    void testStorageDirectoryCreation() {
        // 验证demo用户的存储目录路径
        String demoStoragePath = demoUserConfig.getStoragePath();
        assertEquals("./data/demo/files", demoStoragePath);
        
        // 验证默认存储目录路径
        String defaultStoragePath = defaultConfig.getStoragePath();
        assertEquals("./data/file-transfer/files", defaultStoragePath);
        
        // 验证路径不同
        assertNotEquals(demoStoragePath, defaultStoragePath);
        
        // 验证路径格式正确
        assertTrue(demoStoragePath.startsWith("./data/"));
        assertTrue(defaultStoragePath.startsWith("./data/"));
    }
}
