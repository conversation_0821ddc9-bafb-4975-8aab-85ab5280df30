package com.sdesrd.filetransfer.client.config;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * ClientConfig 统一配置类单元测试
 * 验证合并后的配置类功能完整性和正确性
 */
@DisplayName("客户端统一配置测试")
class ClientConfigTest {
    
    private ClientConfig config;
    
    @BeforeEach
    void setUp() {
        config = new ClientConfig();
        config.setUser("testUser");
        config.setSecretKey("testSecretKey");
    }
    
    // ==================== URL构建测试 ====================
    
    @Test
    @DisplayName("默认配置应该生成正确的URL")
    void testDefaultConfiguration() {
        String expectedUrl = "http://localhost:49011/filetransfer";
        assertEquals(expectedUrl, config.getServerUrl());
    }
    
    @Test
    @DisplayName("自定义服务器地址和端口")
    void testCustomServerAddressAndPort() {
        config.setServerAddr("example.com");
        config.setServerPort(9090);

        String expectedUrl = "http://example.com:9090/filetransfer";
        assertEquals(expectedUrl, config.getServerUrl());
    }
    
    @Test
    @DisplayName("使用HTTPS协议")
    void testHttpsProtocol() {
        config.setUseHttps(true);

        String expectedUrl = "https://localhost:49011/filetransfer";
        assertEquals(expectedUrl, config.getServerUrl());
    }
    
    @Test
    @DisplayName("设置上下文路径")
    void testContextPath() {
        config.setContextPath("api");
        
        String expectedUrl = "http://localhost:49011/api";
        assertEquals(expectedUrl, config.getServerUrl());
    }
    
    @Test
    @DisplayName("上下文路径标准化 - 移除前后斜杠")
    void testContextPathNormalization() {
        // 测试前后都有斜杠的情况
        config.setContextPath("/api/v1/");
        assertEquals("http://localhost:49011/api/v1", config.getServerUrl());
        
        // 测试只有前斜杠的情况
        config.setContextPath("/api");
        assertEquals("http://localhost:49011/api", config.getServerUrl());
        
        // 测试只有后斜杠的情况
        config.setContextPath("api/");
        assertEquals("http://localhost:49011/api", config.getServerUrl());
        
        // 测试多个斜杠的情况
        config.setContextPath("///api/v1///");
        assertEquals("http://localhost:49011/api/v1", config.getServerUrl());
    }
    
    @Test
    @DisplayName("空的上下文路径")
    void testEmptyContextPath() {
        config.setContextPath("");
        assertEquals("http://localhost:49011", config.getServerUrl());

        config.setContextPath(null);
        assertEquals("http://localhost:49011", config.getServerUrl());

        config.setContextPath("   ");
        assertEquals("http://localhost:49011", config.getServerUrl());
    }
    
    @Test
    @DisplayName("复杂的URL构建场景")
    void testComplexUrlBuilding() {
        config.setServerAddr("api.example.com");
        config.setServerPort(443);
        config.setUseHttps(true);
        config.setContextPath("/filetransfer/v2/");

        String expectedUrl = "https://api.example.com:443/filetransfer/v2";
        assertEquals(expectedUrl, config.getServerUrl());
    }
    
    // ==================== 配置验证测试 ====================
    
    @Test
    @DisplayName("验证配置 - 成功案例")
    void testValidConfiguration() {
        // 确保所有必需字段都有有效值
        config.setServerAddr("localhost");
        config.setServerPort(49011);
        config.setUser("testUser");
        config.setSecretKey("testKey");
        config.setChunkSize(1024 * 1024);
        config.setConnectTimeoutSeconds(30);
        config.setReadTimeoutSeconds(60);
        config.setWriteTimeoutSeconds(60);
        config.setMaxConcurrentTransfers(3);
        config.setMaxIdleConnections(5);
        config.setKeepAliveDurationMinutes(5);
        
        assertDoesNotThrow(() -> config.validateConfig());
    }
    
    @Test
    @DisplayName("验证配置 - 服务器地址为空")
    void testValidationEmptyServerAddr() {
        config.setServerAddr("");
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
        
        config.setServerAddr(null);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
        
        config.setServerAddr("   ");
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
    }
    
    @Test
    @DisplayName("验证配置 - 无效端口")
    void testValidationInvalidPort() {
        config.setServerPort(0);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
        
        config.setServerPort(-1);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
        
        config.setServerPort(65536);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
    }
    
    @Test
    @DisplayName("验证配置 - 用户名为空")
    void testValidationEmptyUser() {
        config.setUser("");
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
        
        config.setUser(null);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
        
        config.setUser("   ");
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
    }
    
    @Test
    @DisplayName("验证配置 - 密钥为空")
    void testValidationEmptySecretKey() {
        config.setSecretKey("");
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
        
        config.setSecretKey(null);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
        
        config.setSecretKey("   ");
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
    }
    
    @Test
    @DisplayName("验证配置 - 无效分片大小")
    void testValidationInvalidChunkSize() {
        config.setChunkSize(0);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
        
        config.setChunkSize(-1);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
    }
    
    @Test
    @DisplayName("验证配置 - 无效超时时间")
    void testValidationInvalidTimeouts() {
        config.setConnectTimeoutSeconds(0);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
        
        config.setConnectTimeoutSeconds(30);
        config.setReadTimeoutSeconds(-1);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
        
        config.setReadTimeoutSeconds(60);
        config.setWriteTimeoutSeconds(0);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
    }
    
    @Test
    @DisplayName("验证配置 - 无效并发设置")
    void testValidationInvalidConcurrency() {
        config.setMaxConcurrentTransfers(0);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
        
        config.setMaxConcurrentTransfers(3);
        config.setMaxIdleConnections(-1);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
        
        config.setMaxIdleConnections(5);
        config.setKeepAliveDurationMinutes(0);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
    }
    
    @Test
    @DisplayName("验证配置 - 无效重试设置")
    void testValidationInvalidRetry() {
        config.setRetryCount(-1);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
        
        config.setRetryCount(3);
        config.setRetryIntervalMs(-1);
        assertThrows(IllegalStateException.class, () -> config.validateConfig());
    }
    
    @Test
    @DisplayName("getServerUrl在无效配置时应该抛出异常")
    void testGetServerUrlWithInvalidConfig() {
        config.setServerAddr("");
        assertThrows(IllegalStateException.class, () -> config.getServerUrl());
        
        config.setServerAddr("localhost");
        config.setServerPort(-1);
        assertThrows(IllegalStateException.class, () -> config.getServerUrl());
    }
    
    // ==================== 字段访问测试 ====================
    
    @Test
    @DisplayName("所有字段的getter和setter测试")
    void testAllFieldsGetterSetter() {
        // 服务器连接配置
        config.setServerAddr("test.example.com");
        assertEquals("test.example.com", config.getServerAddr());
        
        config.setServerPort(8080);
        assertEquals(8080, config.getServerPort());
        
        config.setContextPath("test-api");
        assertEquals("test-api", config.getContextPath());
        
        config.setUseHttps(true);
        assertTrue(config.isUseHttps());
        
        // 认证配置
        config.setUser("testUser123");
        assertEquals("testUser123", config.getUser());
        
        config.setSecretKey("testSecret456");
        assertEquals("testSecret456", config.getSecretKey());
        
        // 传输配置
        config.setChunkSize(5 * 1024 * 1024);
        assertEquals(5 * 1024 * 1024, config.getChunkSize());
        
        config.setConnectTimeoutSeconds(45);
        assertEquals(45, config.getConnectTimeoutSeconds());
        
        config.setReadTimeoutSeconds(90);
        assertEquals(90, config.getReadTimeoutSeconds());
        
        config.setWriteTimeoutSeconds(120);
        assertEquals(120, config.getWriteTimeoutSeconds());
        
        // 并发配置
        config.setMaxConcurrentTransfers(10);
        assertEquals(10, config.getMaxConcurrentTransfers());
        
        config.setMaxIdleConnections(15);
        assertEquals(15, config.getMaxIdleConnections());
        
        config.setKeepAliveDurationMinutes(10);
        assertEquals(10, config.getKeepAliveDurationMinutes());
        
        // 重试配置
        config.setRetryCount(5);
        assertEquals(5, config.getRetryCount());
        
        config.setRetryIntervalMs(2000);
        assertEquals(2000, config.getRetryIntervalMs());
    }
    

    
    // ==================== 边界条件测试 ====================
    
    @Test
    @DisplayName("边界条件 - 最小有效端口")
    void testMinValidPort() {
        config.setServerPort(1);
        assertDoesNotThrow(() -> config.validateConfig());
    }
    
    @Test
    @DisplayName("边界条件 - 最大有效端口")
    void testMaxValidPort() {
        config.setServerPort(65535);
        assertDoesNotThrow(() -> config.validateConfig());
    }
    
    @Test
    @DisplayName("边界条件 - 最小分片大小")
    void testMinChunkSize() {
        config.setChunkSize(1);
        assertDoesNotThrow(() -> config.validateConfig());
    }
    
    @Test
    @DisplayName("边界条件 - 零重试次数")
    void testZeroRetryCount() {
        config.setRetryCount(0);
        assertDoesNotThrow(() -> config.validateConfig());
    }
    
    @Test
    @DisplayName("边界条件 - 零重试间隔")
    void testZeroRetryInterval() {
        config.setRetryIntervalMs(0);
        assertDoesNotThrow(() -> config.validateConfig());
    }
} 