package com.sdesrd.filetransfer.client.util;

import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Supplier;

import lombok.extern.slf4j.Slf4j;

/**
 * 重试管理器
 * 支持指数退避和抖动的重试策略
 */
@Slf4j
public class RetryManager {
    
    /** 默认重试次数 */
    private static final int DEFAULT_MAX_RETRIES = 3;
    
    /** 默认基础延迟时间（毫秒） */
    private static final long DEFAULT_BASE_DELAY_MS = 1000L;
    
    /** 默认最大延迟时间（毫秒） */
    private static final long DEFAULT_MAX_DELAY_MS = 30000L;
    
    /** 默认退避倍数 */
    private static final double DEFAULT_BACKOFF_MULTIPLIER = 2.0;
    
    /** 默认抖动因子 */
    private static final double DEFAULT_JITTER_FACTOR = 0.1;
    
    /**
     * 重试策略配置
     */
    public static class RetryPolicy {
        private final int maxRetries;
        private final long baseDelayMs;
        private final long maxDelayMs;
        private final double backoffMultiplier;
        private final double jitterFactor;
        private final Class<? extends Throwable>[] retryableExceptions;
        
        @SafeVarargs
        public RetryPolicy(int maxRetries, long baseDelayMs, long maxDelayMs, 
                          double backoffMultiplier, double jitterFactor,
                          Class<? extends Throwable>... retryableExceptions) {
            this.maxRetries = maxRetries;
            this.baseDelayMs = baseDelayMs;
            this.maxDelayMs = maxDelayMs;
            this.backoffMultiplier = backoffMultiplier;
            this.jitterFactor = jitterFactor;
            this.retryableExceptions = retryableExceptions;
        }
        
        public static RetryPolicy defaultPolicy() {
            return new RetryPolicy(DEFAULT_MAX_RETRIES, DEFAULT_BASE_DELAY_MS, DEFAULT_MAX_DELAY_MS,
                    DEFAULT_BACKOFF_MULTIPLIER, DEFAULT_JITTER_FACTOR);
        }
        
        public static RetryPolicy networkPolicy() {
            return new RetryPolicy(5, 500L, 10000L, 1.5, 0.2);
        }
        
        public static RetryPolicy quickPolicy() {
            return new RetryPolicy(2, 100L, 1000L, 2.0, 0.1);
        }
        
        // Getters
        public int getMaxRetries() { return maxRetries; }
        public long getBaseDelayMs() { return baseDelayMs; }
        public long getMaxDelayMs() { return maxDelayMs; }
        public double getBackoffMultiplier() { return backoffMultiplier; }
        public double getJitterFactor() { return jitterFactor; }
        public Class<? extends Throwable>[] getRetryableExceptions() { return retryableExceptions; }
    }
    
    /**
     * 执行带重试的操作
     * 
     * @param operation 要执行的操作
     * @param policy 重试策略
     * @param operationName 操作名称（用于日志）
     * @return 操作结果
     * @throws Exception 如果所有重试都失败
     */
    public static <T> T executeWithRetry(Supplier<T> operation, RetryPolicy policy, String operationName) throws Exception {
        Exception lastException = null;
        
        for (int attempt = 0; attempt <= policy.getMaxRetries(); attempt++) {
            try {
                if (attempt > 0) {
                    log.debug("重试操作 - 操作: {}, 尝试次数: {}/{}", operationName, attempt, policy.getMaxRetries());
                }
                
                return operation.get();
                
            } catch (Exception e) {
                lastException = e;
                
                // 检查是否是可重试的异常
                if (!isRetryableException(e, policy)) {
                    log.warn("不可重试的异常 - 操作: {}, 异常: {}", operationName, e.getMessage());
                    throw e;
                }
                
                // 如果已经是最后一次尝试，不再重试
                if (attempt >= policy.getMaxRetries()) {
                    log.error("重试次数已达上限 - 操作: {}, 尝试次数: {}, 最后异常: {}", 
                            operationName, attempt + 1, e.getMessage());
                    break;
                }
                
                // 计算延迟时间并等待
                long delayMs = calculateDelay(attempt, policy);
                log.warn("操作失败，将在{}ms后重试 - 操作: {}, 尝试次数: {}/{}, 异常: {}", 
                        delayMs, operationName, attempt + 1, policy.getMaxRetries() + 1, e.getMessage());
                
                try {
                    Thread.sleep(delayMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("重试被中断", ie);
                }
            }
        }
        
        throw new RuntimeException("重试失败 - 操作: " + operationName, lastException);
    }
    
    /**
     * 执行带重试的操作（使用默认策略）
     */
    public static <T> T executeWithRetry(Supplier<T> operation, String operationName) throws Exception {
        return executeWithRetry(operation, RetryPolicy.defaultPolicy(), operationName);
    }
    
    /**
     * 执行带重试的操作（无返回值）
     */
    public static void executeWithRetry(Runnable operation, RetryPolicy policy, String operationName) throws Exception {
        executeWithRetry(() -> {
            operation.run();
            return null;
        }, policy, operationName);
    }
    
    /**
     * 计算延迟时间（指数退避 + 抖动）
     */
    private static long calculateDelay(int attempt, RetryPolicy policy) {
        // 指数退避
        double delay = policy.getBaseDelayMs() * Math.pow(policy.getBackoffMultiplier(), attempt);
        
        // 限制最大延迟
        delay = Math.min(delay, policy.getMaxDelayMs());
        
        // 添加抖动
        double jitter = delay * policy.getJitterFactor() * (ThreadLocalRandom.current().nextDouble() * 2 - 1);
        delay += jitter;
        
        return Math.max(0, (long) delay);
    }
    
    /**
     * 检查异常是否可重试
     */
    private static boolean isRetryableException(Exception e, RetryPolicy policy) {
        // 如果没有指定可重试异常，默认所有异常都可重试
        if (policy.getRetryableExceptions() == null || policy.getRetryableExceptions().length == 0) {
            return true;
        }
        
        // 检查异常类型是否匹配
        for (Class<? extends Throwable> retryableType : policy.getRetryableExceptions()) {
            if (retryableType.isAssignableFrom(e.getClass())) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 创建网络重试策略构建器
     */
    public static RetryPolicyBuilder networkRetry() {
        return new RetryPolicyBuilder()
                .maxRetries(5)
                .baseDelay(500L)
                .maxDelay(10000L)
                .backoffMultiplier(1.5)
                .jitterFactor(0.2);
    }
    
    /**
     * 创建快速重试策略构建器
     */
    public static RetryPolicyBuilder quickRetry() {
        return new RetryPolicyBuilder()
                .maxRetries(2)
                .baseDelay(100L)
                .maxDelay(1000L)
                .backoffMultiplier(2.0)
                .jitterFactor(0.1);
    }
    
    /**
     * 重试策略构建器
     */
    public static class RetryPolicyBuilder {
        private int maxRetries = DEFAULT_MAX_RETRIES;
        private long baseDelayMs = DEFAULT_BASE_DELAY_MS;
        private long maxDelayMs = DEFAULT_MAX_DELAY_MS;
        private double backoffMultiplier = DEFAULT_BACKOFF_MULTIPLIER;
        private double jitterFactor = DEFAULT_JITTER_FACTOR;
        private Class<? extends Throwable>[] retryableExceptions;
        
        public RetryPolicyBuilder maxRetries(int maxRetries) {
            this.maxRetries = maxRetries;
            return this;
        }
        
        public RetryPolicyBuilder baseDelay(long baseDelayMs) {
            this.baseDelayMs = baseDelayMs;
            return this;
        }
        
        public RetryPolicyBuilder maxDelay(long maxDelayMs) {
            this.maxDelayMs = maxDelayMs;
            return this;
        }
        
        public RetryPolicyBuilder backoffMultiplier(double backoffMultiplier) {
            this.backoffMultiplier = backoffMultiplier;
            return this;
        }
        
        public RetryPolicyBuilder jitterFactor(double jitterFactor) {
            this.jitterFactor = jitterFactor;
            return this;
        }
        
        @SafeVarargs
        public final RetryPolicyBuilder retryOn(Class<? extends Throwable>... exceptions) {
            this.retryableExceptions = exceptions;
            return this;
        }
        
        public RetryPolicy build() {
            return new RetryPolicy(maxRetries, baseDelayMs, maxDelayMs, 
                    backoffMultiplier, jitterFactor, retryableExceptions);
        }
    }
}
