package com.sdesrd.filetransfer.client.dto;

import lombok.Data;

/**
 * 传输进度
 */
@Data
public class TransferProgress {
    
    /**
     * 传输ID
     */
    private String transferId;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 总大小（字节）
     */
    private Long totalSize;
    
    /**
     * 已传输大小（字节）
     */
    private Long transferredSize;
    
    /**
     * 进度百分比（0-100）
     */
    private Double progress;
    
    /**
     * 总分块数
     */
    private Integer totalChunks;
    
    /**
     * 已完成分块数
     */
    private Integer completedChunks;
    
    /**
     * 是否完成
     */
    private boolean completed;
    
    /**
     * 传输速度（字节/秒）
     */
    private Long speed;
    
    /**
     * 剩余时间（秒）
     */
    private Long remainingTime;
} 