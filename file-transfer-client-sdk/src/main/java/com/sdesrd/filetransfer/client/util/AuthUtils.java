package com.sdesrd.filetransfer.client.util;

import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;

/**
 * 客户端认证工具类
 */
@Slf4j
public class AuthUtils {
    
    private static final String HMAC_SHA256 = "HmacSHA256";
    public static final String AUTH_HEADER = "X-File-Transfer-Auth";
    public static final String USER_HEADER = "X-File-Transfer-User";
    
    /**
     * 生成认证令牌
     *
     * @param username 用户名
     * @param secretKey 密钥
     * @return 认证令牌
     */
    public static String generateAuthToken(String username, String secretKey) {
        try {
            long timestamp = System.currentTimeMillis();
            String signature = generateSignature(username, timestamp, secretKey);
            return username + ":" + timestamp + ":" + signature;
        } catch (Exception e) {
            log.error("生成认证令牌失败: {}", username, e);
            throw new RuntimeException("生成认证令牌失败", e);
        }
    }
    
    /**
     * 生成HMAC-SHA256签名
     * 
     * @param username 用户名
     * @param timestamp 时间戳
     * @param secretKey 密钥
     * @return 签名
     */
    private static String generateSignature(String username, long timestamp, String secretKey) 
            throws NoSuchAlgorithmException, InvalidKeyException {
        String data = username + ":" + timestamp;
        Mac mac = Mac.getInstance(HMAC_SHA256);
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
        mac.init(secretKeySpec);
        byte[] signature = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(signature);
    }

    /**
     * 为请求添加认证头
     *
     * @param requestBuilder 请求构建器
     * @param username 用户名
     * @param secretKey 密钥
     * @return 请求构建器
     */
    public static Request.Builder addAuthHeaders(Request.Builder requestBuilder, String username, String secretKey) {
        String authToken = generateAuthToken(username, secretKey);
        return requestBuilder
                .addHeader(USER_HEADER, username)
                .addHeader(AUTH_HEADER, authToken);
    }
}