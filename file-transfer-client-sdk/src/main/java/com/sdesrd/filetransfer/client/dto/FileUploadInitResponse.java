package com.sdesrd.filetransfer.client.dto;

import lombok.Data;

/**
 * 文件上传初始化响应
 * 重构后的版本：使用ULID作为fileId，返回最终存储的文件名
 */
@Data
public class FileUploadInitResponse {

    /**
     * 传输ID（UUID格式）
     */
    private String transferId;

    /**
     * 文件ID（ULID格式）
     */
    private String fileId;

    /**
     * 最终存储的文件名（格式：md5.{后缀名} 或 md5）
     */
    private String fileName;

    /**
     * 是否秒传
     */
    private Boolean fastUpload;

    /**
     * 分块大小
     */
    private Long chunkSize;

    /**
     * 总分块数
     */
    private Integer totalChunks;
}