package com.sdesrd.filetransfer.client.util;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import lombok.extern.slf4j.Slf4j;

/**
 * 并发传输管理器
 * 管理文件传输的并发控制和资源分配
 */
@Slf4j
public class ConcurrentTransferManager {
    
    /** 默认最大并发传输数 */
    private static final int DEFAULT_MAX_CONCURRENT_TRANSFERS = 3;
    
    /** 默认线程池大小 */
    private static final int DEFAULT_THREAD_POOL_SIZE = 8;
    
    /** 传输任务计数器 */
    private static final AtomicInteger TRANSFER_COUNTER = new AtomicInteger(0);
    
    /** 活跃传输统计 */
    private static final AtomicLong ACTIVE_UPLOADS = new AtomicLong(0);
    private static final AtomicLong ACTIVE_DOWNLOADS = new AtomicLong(0);
    private static final AtomicLong TOTAL_UPLOADED_BYTES = new AtomicLong(0);
    private static final AtomicLong TOTAL_DOWNLOADED_BYTES = new AtomicLong(0);
    
    /** 活跃传输任务跟踪 */
    private static final ConcurrentHashMap<String, TransferTask> ACTIVE_TRANSFERS = new ConcurrentHashMap<>();
    
    private final int maxConcurrentTransfers;
    private final ExecutorService executorService;
    private final Semaphore transferSemaphore;
    
    public ConcurrentTransferManager(int maxConcurrentTransfers) {
        this.maxConcurrentTransfers = Math.max(1, maxConcurrentTransfers);
        this.transferSemaphore = new Semaphore(this.maxConcurrentTransfers);
        
        // 创建线程池
        int threadPoolSize = Math.max(this.maxConcurrentTransfers, DEFAULT_THREAD_POOL_SIZE);
        this.executorService = Executors.newFixedThreadPool(threadPoolSize, new TransferThreadFactory());
        
        log.info("并发传输管理器初始化 - 最大并发数: {}, 线程池大小: {}", 
                this.maxConcurrentTransfers, threadPoolSize);
    }
    
    /**
     * 提交上传任务
     * 
     * @param taskId 任务ID
     * @param uploadTask 上传任务
     * @return 任务Future
     */
    public <T> CompletableFuture<T> submitUploadTask(String taskId, TransferCallable<T> uploadTask) {
        return submitTransferTask(taskId, TransferType.UPLOAD, uploadTask);
    }
    
    /**
     * 提交下载任务
     * 
     * @param taskId 任务ID
     * @param downloadTask 下载任务
     * @return 任务Future
     */
    public <T> CompletableFuture<T> submitDownloadTask(String taskId, TransferCallable<T> downloadTask) {
        return submitTransferTask(taskId, TransferType.DOWNLOAD, downloadTask);
    }
    
    /**
     * 提交传输任务
     */
    private <T> CompletableFuture<T> submitTransferTask(String taskId, TransferType type, TransferCallable<T> task) {
        return CompletableFuture.supplyAsync(() -> {
            // 获取传输许可
            try {
                transferSemaphore.acquire();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new RuntimeException("获取传输许可被中断", e);
            }
            
            // 创建传输任务记录
            TransferTask transferTask = new TransferTask(taskId, type, System.currentTimeMillis());
            ACTIVE_TRANSFERS.put(taskId, transferTask);
            
            // 更新统计
            if (type == TransferType.UPLOAD) {
                ACTIVE_UPLOADS.incrementAndGet();
            } else {
                ACTIVE_DOWNLOADS.incrementAndGet();
            }
            
            log.debug("开始传输任务 - ID: {}, 类型: {}, 活跃传输数: {}", 
                    taskId, type, ACTIVE_TRANSFERS.size());
            
            try {
                // 执行传输任务
                T result = task.call();
                
                // 更新成功统计
                transferTask.setCompleted(true);
                transferTask.setEndTime(System.currentTimeMillis());
                
                log.debug("传输任务完成 - ID: {}, 类型: {}, 耗时: {}ms", 
                        taskId, type, transferTask.getDuration());
                
                return result;
                
            } catch (Exception e) {
                // 更新失败统计
                transferTask.setFailed(true);
                transferTask.setEndTime(System.currentTimeMillis());
                transferTask.setErrorMessage(e.getMessage());
                
                log.error("传输任务失败 - ID: {}, 类型: {}, 耗时: {}ms, 错误: {}", 
                        taskId, type, transferTask.getDuration(), e.getMessage());
                
                throw new RuntimeException("传输任务失败: " + e.getMessage(), e);
                
            } finally {
                // 清理资源
                ACTIVE_TRANSFERS.remove(taskId);
                
                // 更新统计
                if (type == TransferType.UPLOAD) {
                    ACTIVE_UPLOADS.decrementAndGet();
                } else {
                    ACTIVE_DOWNLOADS.decrementAndGet();
                }
                
                // 释放传输许可
                transferSemaphore.release();
                
                log.debug("传输任务清理完成 - ID: {}, 类型: {}, 剩余活跃传输数: {}", 
                        taskId, type, ACTIVE_TRANSFERS.size());
            }
        }, executorService);
    }
    
    /**
     * 取消传输任务
     * 
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    public boolean cancelTransfer(String taskId) {
        TransferTask task = ACTIVE_TRANSFERS.get(taskId);
        if (task != null) {
            task.setCancelled(true);
            log.info("传输任务已标记为取消 - ID: {}", taskId);
            return true;
        }
        return false;
    }
    
    /**
     * 获取传输统计信息
     */
    public TransferStats getTransferStats() {
        return new TransferStats(
                ACTIVE_UPLOADS.get(),
                ACTIVE_DOWNLOADS.get(),
                TOTAL_UPLOADED_BYTES.get(),
                TOTAL_DOWNLOADED_BYTES.get(),
                ACTIVE_TRANSFERS.size(),
                maxConcurrentTransfers
        );
    }
    
    /**
     * 更新上传字节数统计
     */
    public static void addUploadedBytes(long bytes) {
        TOTAL_UPLOADED_BYTES.addAndGet(bytes);
    }
    
    /**
     * 更新下载字节数统计
     */
    public static void addDownloadedBytes(long bytes) {
        TOTAL_DOWNLOADED_BYTES.addAndGet(bytes);
    }
    
    /**
     * 检查是否有可用的传输槽位
     */
    public boolean hasAvailableSlot() {
        return transferSemaphore.availablePermits() > 0;
    }
    
    /**
     * 获取可用传输槽位数
     */
    public int getAvailableSlots() {
        return transferSemaphore.availablePermits();
    }
    
    /**
     * 关闭传输管理器
     */
    public void shutdown() {
        log.info("关闭并发传输管理器...");
        
        // 取消所有活跃传输
        ACTIVE_TRANSFERS.values().forEach(task -> task.setCancelled(true));
        
        // 关闭线程池
        executorService.shutdown();
        
        log.info("并发传输管理器已关闭");
    }
    
    /**
     * 传输类型枚举
     */
    public enum TransferType {
        UPLOAD("上传"),
        DOWNLOAD("下载");
        
        private final String description;
        
        TransferType(String description) {
            this.description = description;
        }
        
        @Override
        public String toString() {
            return description;
        }
    }
    
    /**
     * 传输任务接口
     */
    @FunctionalInterface
    public interface TransferCallable<T> {
        T call() throws Exception;
    }
    
    /**
     * 传输任务记录
     */
    public static class TransferTask {
        private final String taskId;
        private final TransferType type;
        private final long startTime;
        private long endTime;
        private boolean completed;
        private boolean failed;
        private boolean cancelled;
        private String errorMessage;
        
        public TransferTask(String taskId, TransferType type, long startTime) {
            this.taskId = taskId;
            this.type = type;
            this.startTime = startTime;
        }
        
        public long getDuration() {
            return (endTime > 0 ? endTime : System.currentTimeMillis()) - startTime;
        }
        
        // Getters and setters
        public String getTaskId() { return taskId; }
        public TransferType getType() { return type; }
        public long getStartTime() { return startTime; }
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public boolean isCompleted() { return completed; }
        public void setCompleted(boolean completed) { this.completed = completed; }
        public boolean isFailed() { return failed; }
        public void setFailed(boolean failed) { this.failed = failed; }
        public boolean isCancelled() { return cancelled; }
        public void setCancelled(boolean cancelled) { this.cancelled = cancelled; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
    
    /**
     * 传输统计信息
     */
    public static class TransferStats {
        private final long activeUploads;
        private final long activeDownloads;
        private final long totalUploadedBytes;
        private final long totalDownloadedBytes;
        private final int activeTransfers;
        private final int maxConcurrentTransfers;
        
        public TransferStats(long activeUploads, long activeDownloads, long totalUploadedBytes, 
                           long totalDownloadedBytes, int activeTransfers, int maxConcurrentTransfers) {
            this.activeUploads = activeUploads;
            this.activeDownloads = activeDownloads;
            this.totalUploadedBytes = totalUploadedBytes;
            this.totalDownloadedBytes = totalDownloadedBytes;
            this.activeTransfers = activeTransfers;
            this.maxConcurrentTransfers = maxConcurrentTransfers;
        }
        
        // Getters
        public long getActiveUploads() { return activeUploads; }
        public long getActiveDownloads() { return activeDownloads; }
        public long getTotalUploadedBytes() { return totalUploadedBytes; }
        public long getTotalDownloadedBytes() { return totalDownloadedBytes; }
        public int getActiveTransfers() { return activeTransfers; }
        public int getMaxConcurrentTransfers() { return maxConcurrentTransfers; }
        
        @Override
        public String toString() {
            return String.format("传输统计 - 活跃上传: %d, 活跃下载: %d, 总上传: %s, 总下载: %s, 活跃传输: %d/%d",
                    activeUploads, activeDownloads,
                    FileUtils.formatFileSize(totalUploadedBytes),
                    FileUtils.formatFileSize(totalDownloadedBytes),
                    activeTransfers, maxConcurrentTransfers);
        }
    }
    
    /**
     * 传输线程工厂
     */
    private static class TransferThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        
        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, "TransferManager-" + threadNumber.getAndIncrement());
            t.setDaemon(true);
            t.setPriority(Thread.NORM_PRIORITY);
            return t;
        }
    }
}
