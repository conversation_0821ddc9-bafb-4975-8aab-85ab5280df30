package com.sdesrd.filetransfer.client.util;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.RandomAccessFile;

import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.listener.TransferListener;

import cn.hutool.crypto.digest.DigestUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;

/**
 * 文件工具类
 */
@Slf4j
public class FileUtils {
    
    /**
     * 计算文件MD5
     * 
     * @param file 文件
     * @return MD5值
     */
    public static String calculateMD5(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file)) {
            return DigestUtil.md5Hex(fis);
        }
    }
    
    /**
     * 计算字节数组MD5
     * 
     * @param data 字节数组
     * @return MD5值
     */
    public static String calculateMD5(byte[] data) {
        return DigestUtil.md5Hex(data);
    }
    
    /**
     * 读取文件分块
     * 
     * @param file       文件
     * @param offset     偏移量
     * @param chunkSize  分块大小
     * @return 分块数据
     */
    public static byte[] readFileChunk(File file, long offset, long chunkSize) throws IOException {
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            raf.seek(offset);
            
            byte[] buffer = new byte[(int) chunkSize];
            int bytesRead = raf.read(buffer);
            
            if (bytesRead < chunkSize) {
                // 如果读取的字节数小于分块大小，创建一个新的数组
                byte[] actualData = new byte[bytesRead];
                System.arraycopy(buffer, 0, actualData, 0, bytesRead);
                return actualData;
            }
            
            return buffer;
        }
    }
    
    /**
     * 保存响应到文件
     * 
     * @param response HTTP响应
     * @param file     目标文件
     * @param listener 传输监听器（可选）
     */
    public static void saveResponseToFile(Response response, File file, TransferListener listener) throws IOException {
        // 确保父目录存在
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        long contentLength = getContentLength(response);
        long downloadedBytes = 0;
        
        if (response.body() == null) {
            throw new IOException("响应体为空");
        }
        
        try (InputStream inputStream = response.body().byteStream();
            FileOutputStream outputStream = new FileOutputStream(file);
            BufferedOutputStream bufferedOut = new BufferedOutputStream(outputStream)) {
            
            byte[] buffer = new byte[8192]; // 8KB缓冲区
            int bytesRead;
            long startTime = System.currentTimeMillis();
            
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                bufferedOut.write(buffer, 0, bytesRead);
                downloadedBytes += bytesRead;
                
                // 更新进度
                if (listener != null && contentLength > 0) {
                    TransferProgress progress = new TransferProgress();
                    progress.setFileName(file.getName());
                    progress.setTotalSize(contentLength);
                    progress.setTransferredSize(downloadedBytes);
                    progress.setProgress((double) downloadedBytes / contentLength * 100);
                    progress.setCompleted(downloadedBytes >= contentLength);
                    
                    // 计算速度
                    long elapsed = System.currentTimeMillis() - startTime;
                    if (elapsed > 0) {
                        progress.setSpeed(downloadedBytes * 1000 / elapsed);
                        if (progress.getSpeed() > 0) {
                            progress.setRemainingTime((contentLength - downloadedBytes) / progress.getSpeed());
                        }
                    }
                    
                    listener.onProgress(progress);
                    
                    if (progress.isCompleted()) {
                        listener.onCompleted(progress);
                    }
                }
            }
        }
    }
    
    /**
     * 保存响应到文件（支持断点续传）
     * 
     * @param response HTTP响应
     * @param file     目标文件
     * @param startPos 起始位置（用于断点续传）
     * @param listener 传输监听器（可选）
     */
    public static void saveResponseToFileWithResume(Response response, File file, long startPos, TransferListener listener) throws IOException {
        // 确保父目录存在
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        long contentLength = getContentLength(response);
        long totalSize = startPos + contentLength; // 文件总大小
        long downloadedBytes = startPos; // 已下载字节数（包括之前下载的部分）
        
        if (response.body() == null) {
            throw new IOException("响应体为空");
        }
        
        // 使用追加模式打开文件
        try (InputStream inputStream = response.body().byteStream();
            FileOutputStream outputStream = new FileOutputStream(file, true); // true表示追加模式
            BufferedOutputStream bufferedOut = new BufferedOutputStream(outputStream)) {
            
            byte[] buffer = new byte[8192]; // 8KB缓冲区
            int bytesRead;
            long startTime = System.currentTimeMillis();
            long currentDownloadBytes = 0; // 本次下载的字节数
            
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                bufferedOut.write(buffer, 0, bytesRead);
                currentDownloadBytes += bytesRead;
                downloadedBytes += bytesRead;
                
                // 更新进度
                if (listener != null) {
                    TransferProgress progress = new TransferProgress();
                    progress.setFileName(file.getName());
                    
                    // 如果能获取到内容长度，计算总大小
                    if (contentLength > 0) {
                        progress.setTotalSize(totalSize);
                        progress.setTransferredSize(downloadedBytes);
                        progress.setProgress((double) downloadedBytes / totalSize * 100);
                        progress.setCompleted(downloadedBytes >= totalSize);
                    } else {
                        // 无法确定总大小，只显示已下载的字节数
                        progress.setTotalSize(-1L);
                        progress.setTransferredSize(downloadedBytes);
                        progress.setProgress(-1.0); // 无法计算百分比
                        progress.setCompleted(false);
                    }
                    
                    // 计算速度（基于本次下载的数据）
                    long elapsed = System.currentTimeMillis() - startTime;
                    if (elapsed > 0) {
                        progress.setSpeed(currentDownloadBytes * 1000 / elapsed);
                        if (progress.getSpeed() > 0 && contentLength > 0) {
                            long remainingBytes = contentLength - currentDownloadBytes;
                            progress.setRemainingTime(remainingBytes / progress.getSpeed());
                        }
                    }
                    
                    listener.onProgress(progress);
                    
                    if (progress.isCompleted()) {
                        listener.onCompleted(progress);
                    }
                }
            }
            
            // 如果无法确定内容长度，在下载完成后通知
            if (listener != null && contentLength <= 0) {
                TransferProgress finalProgress = new TransferProgress();
                finalProgress.setFileName(file.getName());
                finalProgress.setTotalSize(downloadedBytes);
                finalProgress.setTransferredSize(downloadedBytes);
                finalProgress.setProgress(100.0);
                finalProgress.setCompleted(true);
                listener.onCompleted(finalProgress);
            }
        }
    }
    
    /**
     * 获取响应内容长度
     */
    private static long getContentLength(Response response) {
        String contentLength = response.header("Content-Length");
        if (contentLength != null) {
            try {
                return Long.parseLong(contentLength);
            } catch (NumberFormatException e) {
                log.warn("无法解析Content-Length: {}", contentLength);
            }
        }
        return -1;
    }
    
    /**
     * 格式化文件大小
     * 
     * @param size 文件大小（字节）
     * @return 格式化后的大小
     */
    public static String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024 * 1024));
        }
    }
    
    /**
     * 格式化传输速度
     * 
     * @param bytesPerSecond 每秒字节数
     * @return 格式化后的速度
     */
    public static String formatSpeed(long bytesPerSecond) {
        return formatFileSize(bytesPerSecond) + "/s";
    }
    
    /**
     * 格式化时间
     *
     * @param seconds 秒数
     * @return 格式化后的时间
     */
    public static String formatTime(long seconds) {
        if (seconds < 60) {
            return seconds + "秒";
        } else if (seconds < 3600) {
            return (seconds / 60) + "分" + (seconds % 60) + "秒";
        } else {
            long hours = seconds / 3600;
            long minutes = (seconds % 3600) / 60;
            long secs = seconds % 60;
            return hours + "时" + minutes + "分" + secs + "秒";
        }
    }

    /**
     * 下载输入流到文件
     *
     * @param inputStream 输入流
     * @param targetFile 目标文件
     * @param totalSize 总大小
     * @param progressCallback 进度回调
     * @return 下载的字节数
     */
    public static long downloadToFile(InputStream inputStream, File targetFile, long totalSize,
                                     ProgressCallback progressCallback) throws IOException {
        // 确保父目录存在
        File parentDir = targetFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            if (!parentDir.mkdirs()) {
                throw new IOException("无法创建目录: " + parentDir.getAbsolutePath());
            }
        }

        long downloadedBytes = 0;

        try (FileOutputStream outputStream = new FileOutputStream(targetFile);
             BufferedOutputStream bufferedOut = new BufferedOutputStream(outputStream)) {

            byte[] buffer = new byte[8192]; // 8KB缓冲区
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                bufferedOut.write(buffer, 0, bytesRead);
                downloadedBytes += bytesRead;

                // 更新进度
                if (progressCallback != null) {
                    progressCallback.onProgress(downloadedBytes, totalSize);
                }
            }
        }

        return downloadedBytes;
    }

    /**
     * 进度回调接口
     */
    @FunctionalInterface
    public interface ProgressCallback {
        void onProgress(long transferred, long total);
    }
}