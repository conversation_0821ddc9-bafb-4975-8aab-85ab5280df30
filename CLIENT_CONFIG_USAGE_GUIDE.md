# 客户端配置使用指南

**文件传输SDK 3.0.0 - 简洁统一的配置接口**

## 概述

文件传输SDK提供了简洁统一的`ClientConfig`配置类，通过`ClientConfigBuilder`进行链式配置，让文件传输功能集成变得简单直观。

✅ **统一配置**: 所有配置项在同一层级  
✅ **链式API**: 支持流畅的方法链调用  
✅ **预设模板**: 提供多种场景的配置模板  
✅ **类型安全**: 编译时配置验证  

## 基础配置

### 最简配置

```java
// 只需3个参数即可快速连接
ClientConfig config = ClientConfigBuilder.quickConnect(
    "api.example.com", "username", "password");

FileTransferClient client = new FileTransferClient(config);
```

### 标准配置

```java
ClientConfig config = ClientConfigBuilder.create()
    .serverAddr("127.0.0.1")
    .serverPort(49011)
    .contextPath("file-transfer")
    .auth("admin", "admin-secret-key")
    .chunkSize(5 * 1024 * 1024L)  // 5MB分块
    .timeouts(120)  // 统一设置超时为120秒
    .maxConcurrentTransfers(3)
    .retry(5, 1000)  // 5次重试，间隔1秒
    .build();
```

## 预设配置模板

### 本地开发环境

```java
ClientConfig config = ClientConfigBuilder.localConfig("dev", "dev-secret");
// 自动配置：localhost:49011, 1MB分块, 3并发, 3重试
```

### 生产环境

```java
ClientConfig config = ClientConfigBuilder.productionConfig(
    "prod.example.com", 443, "prod-user", "prod-secret");
// 自动配置：HTTPS, 2MB分块, 5并发, 5重试
```

### 高性能传输

```java
ClientConfig config = ClientConfigBuilder.highPerformanceConfig(
    "fast.example.com", "user", "secret");
// 自动配置：4MB分块, 8并发, 120秒超时, 快速重试
```

## 详细配置选项

### 服务器连接配置

```java
ClientConfig config = ClientConfigBuilder.create()
    .serverAddr("api.example.com")     // 服务器地址
    .serverPort(8443)                  // 服务器端口
    .contextPath("api/v2")             // API路径前缀
    .useHttps()                        // 启用HTTPS
    .auth("user", "secret")            // 用户认证
    .build();
```

### 传输性能配置

```java
ClientConfig config = ClientConfigBuilder.create()
    .auth("user", "secret")
    .chunkSize(8 * 1024 * 1024L)       // 8MB分块（大文件优化）
    .timeouts(180)                     // 统一超时180秒
    .build();

// 或分别设置超时
ClientConfig config = ClientConfigBuilder.create()
    .auth("user", "secret")
    .connectTimeout(30)                // 连接超时30秒
    .readTimeout(120)                  // 读取超时120秒
    .writeTimeout(180)                 // 写入超时180秒
    .build();
```

### 并发控制配置

```java
ClientConfig config = ClientConfigBuilder.create()
    .auth("user", "secret")
    .maxConcurrentTransfers(8)         // 最大并发传输数
    .maxIdleConnections(12)            // 连接池大小
    .keepAliveDuration(10)             // 连接保活10分钟
    .build();
```

### 重试策略配置

```java
ClientConfig config = ClientConfigBuilder.create()
    .auth("user", "secret")
    .retry(5, 1000)                    // 5次重试，间隔1秒
    .build();

// 或分别设置
ClientConfig config = ClientConfigBuilder.create()
    .auth("user", "secret")
    .retryCount(3)                     // 重试3次
    .retryInterval(2000)               // 间隔2秒
    .build();
```

## 完整使用示例

### 文件上传示例

```java
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.FileTransferClient;

public class FileUploadExample {
    public static void main(String[] args) {
        // 创建客户端配置
        ClientConfig config = ClientConfigBuilder.create()
            .serverAddr("file.example.com")
            .serverPort(443)
            .useHttps()
            .auth("uploader", "upload-secret-2024")
            .chunkSize(4 * 1024 * 1024L)    // 4MB分块
            .maxConcurrentTransfers(5)       // 5个并发
            .timeouts(300)                   // 5分钟超时
            .retry(3, 2000)                  // 3次重试
            .build();
        
        // 创建客户端
        try (FileTransferClient client = new FileTransferClient(config)) {
            // 上传文件
            CompletableFuture<UploadResult> result = client.uploadFile(
                "/local/path/bigfile.zip",
                null,  // 使用原文件名
                progress -> {
                    System.out.printf("上传进度: %.1f%%\n", progress.getProgress());
                }
            );
            
            UploadResult uploadResult = result.get();
            System.out.println("上传成功，文件ID: " + uploadResult.getFileId());
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
```

### 文件下载示例

```java
public class FileDownloadExample {
    public static void main(String[] args) {
        // 使用预设的高性能配置
        ClientConfig config = ClientConfigBuilder.highPerformanceConfig(
            "file.example.com", "downloader", "download-secret-2024");
        
        try (FileTransferClient client = new FileTransferClient(config)) {
            // 下载文件
            CompletableFuture<DownloadResult> result = client.downloadFile(
                "01H9X2...",  // 文件ID
                "/local/save/downloaded-file.zip",
                progress -> {
                    System.out.printf("下载进度: %.1f%% (%s/%s)\n", 
                        progress.getProgress(),
                        formatSize(progress.getTransferredSize()),
                        formatSize(progress.getTotalSize()));
                }
            );
            
            DownloadResult downloadResult = result.get();
            System.out.println("下载完成: " + downloadResult.getSavedPath());
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static String formatSize(long bytes) {
        if (bytes < 1024) return bytes + "B";
        if (bytes < 1024 * 1024) return String.format("%.1fKB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1fMB", bytes / (1024.0 * 1024));
        return String.format("%.1fGB", bytes / (1024.0 * 1024 * 1024));
    }
}
```

## 配置最佳实践

### 根据场景选择配置

1. **小文件频繁传输**: 使用较小分块(1-2MB)，提高并发数
2. **大文件传输**: 使用大分块(4-8MB)，增加超时时间
3. **网络不稳定**: 增加重试次数和间隔，降低并发数
4. **高并发场景**: 合理设置连接池大小和保活时间

### 性能调优建议

```java
// 大文件高性能配置
ClientConfig bigFileConfig = ClientConfigBuilder.create()
    .serverAddr("big.example.com")
    .auth("user", "secret")
    .chunkSize(8 * 1024 * 1024L)       // 8MB分块减少请求数
    .maxConcurrentTransfers(4)         // 适中并发避免资源争用
    .timeouts(600)                     // 10分钟超时应对慢速网络
    .maxIdleConnections(8)             // 更大连接池
    .keepAliveDuration(15)             // 更长保活时间
    .retry(2, 3000)                    // 减少重试次数但增加间隔
    .build();

// 小文件快速传输配置
ClientConfig smallFileConfig = ClientConfigBuilder.create()
    .serverAddr("fast.example.com")
    .auth("user", "secret")
    .chunkSize(512 * 1024L)            // 512KB小分块
    .maxConcurrentTransfers(10)        // 高并发
    .timeouts(60)                      // 短超时快速失败
    .retry(5, 500)                     // 快速重试
    .build();
```

## 常见问题

### Q: 如何选择合适的分块大小？

A: 
- **512KB-1MB**: 适合小文件或网络不稳定环境
- **2-4MB**: 通用场景的平衡选择
- **4-8MB**: 大文件传输，减少网络开销
- **8MB+**: 超大文件，但注意内存使用

### Q: 并发数设置多少合适？

A: 
- **1-3**: 网络带宽有限或服务器负载高
- **3-5**: 通用场景
- **5-10**: 高性能网络和强服务器
- **10+**: 谨慎使用，可能导致资源争用

### Q: 超时时间如何设置？

A: 根据文件大小和网络状况：
- **小文件(< 10MB)**: 30-60秒
- **中等文件(10MB-100MB)**: 60-300秒  
- **大文件(> 100MB)**: 300-600秒或更长

### Q: 重试策略建议？

A:
- **稳定网络**: 2-3次重试，间隔1-2秒
- **不稳定网络**: 3-5次重试，间隔2-5秒
- **移动网络**: 5-7次重试，使用指数退避

---

**🚀 简洁的统一配置让文件传输变得更简单！根据实际场景选择合适的配置模板，快速集成文件传输功能。** 