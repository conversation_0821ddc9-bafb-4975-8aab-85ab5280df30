@echo off
REM ================================================================================
REM Java环境变量设置脚本 (Windows版本)
REM 用途: 设置正确的Java 8环境变量
REM ================================================================================

setlocal enabledelayedexpansion

REM Java 8环境路径 (Windows格式)
REM 注意：请根据实际安装路径修改以下路径
set "DEFAULT_JAVA8_HOME=%USERPROFILE%\.jdks\corretto-1.8.0_452"

REM 如果默认路径不存在，尝试常见的Java安装路径
if not exist "%DEFAULT_JAVA8_HOME%" (
    REM 尝试Program Files路径
    if exist "%ProgramFiles%\Amazon Corretto\jdk1.8.0_452" (
        set "DEFAULT_JAVA8_HOME=%ProgramFiles%\Amazon Corretto\jdk1.8.0_452"
    ) else if exist "%ProgramFiles(x86)%\Amazon Corretto\jdk1.8.0_452" (
        set "DEFAULT_JAVA8_HOME=%ProgramFiles(x86)%\Amazon Corretto\jdk1.8.0_452"
    ) else if exist "%ProgramFiles%\Java\jdk1.8.0_452" (
        set "DEFAULT_JAVA8_HOME=%ProgramFiles%\Java\jdk1.8.0_452"
    ) else if exist "%ProgramFiles(x86)%\Java\jdk1.8.0_452" (
        set "DEFAULT_JAVA8_HOME=%ProgramFiles(x86)%\Java\jdk1.8.0_452"
    )
)

REM 设置Java环境变量
set "JAVA_HOME=%DEFAULT_JAVA8_HOME%"
set "PATH=%JAVA_HOME%\bin;%PATH%"

REM Maven环境变量
set "MAVEN_OPTS=-Dmaven.compiler.source=1.8 -Dmaven.compiler.target=1.8 -Xmx2g -XX:MaxMetaspaceSize=512m"
set "MAVEN_OPTS=%MAVEN_OPTS% -Djava.security.policy=all.policy"
set "MAVEN_OPTS=%MAVEN_OPTS% -Djava.home=%JAVA_HOME%"

REM 验证Java安装
if not exist "%JAVA_HOME%\bin\java.exe" (
    echo [错误] Java JDK未找到: %JAVA_HOME%
    echo 请检查Java安装路径或修改脚本中的DEFAULT_JAVA8_HOME变量
    exit /b 1
)

echo Java环境变量已设置:
echo   JAVA_HOME: %JAVA_HOME%

REM 获取Java版本信息
"%JAVA_HOME%\bin\java.exe" -version 2>&1 | findstr "version" > temp_version.txt
set /p JAVA_VERSION_LINE=<temp_version.txt
del temp_version.txt
echo   Java版本: %JAVA_VERSION_LINE%

echo.
echo Maven选项: %MAVEN_OPTS%
echo.
echo 环境变量设置完成！

REM 导出环境变量到调用者环境
endlocal & (
    set "JAVA_HOME=%JAVA_HOME%"
    set "PATH=%PATH%"
    set "MAVEN_OPTS=%MAVEN_OPTS%"
)
