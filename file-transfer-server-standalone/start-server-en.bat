@echo off
REM ================================================================================
REM Simple File Transfer Server Start Script (Enhanced)
REM ================================================================================

setlocal enabledelayedexpansion

echo ========================================================
echo     Simple File Transfer Server Start Script
echo     Version: 1.1.0-ENHANCED
echo     Time: %date% %time%
echo ========================================================

REM Default configuration
set "JAR_FILE=target\file-transfer-server-standalone-1.0.0.jar"
set "DEFAULT_SERVER_PORT=49011"
set "DEFAULT_JAVA_OPTS=-Xms512m -Xmx1g -XX:+UseG1GC"
set "LOG_DIR=logs"
set "LOG_FILE=%LOG_DIR%\server.log"

REM Initialize variables
set "SERVER_PORT=%DEFAULT_SERVER_PORT%"
set "JAVA_HOME_CUSTOM="
set "BACKGROUND_MODE=false"
set "COMMAND="

REM Parse command line arguments
call :parse_arguments %*

REM Execute command
if "%COMMAND%"=="--help" goto :show_help
if "%COMMAND%"=="help" goto :show_help
if "%COMMAND%"=="start" goto :start_server
if "%COMMAND%"=="stop" goto :stop_server
if "%COMMAND%"=="status" goto :show_status
if "%COMMAND%"=="" goto :start_server

echo [ERROR] Unknown command: %COMMAND%
goto :show_help

REM ==================== Argument Parsing Function ====================
:parse_arguments
:parse_loop
if "%~1"=="" goto :parse_done

REM Check for commands
if "%~1"=="start" (
    set "COMMAND=start"
    shift
    goto :parse_loop
)
if "%~1"=="stop" (
    set "COMMAND=stop"
    shift
    goto :parse_loop
)
if "%~1"=="status" (
    set "COMMAND=status"
    shift
    goto :parse_loop
)
if "%~1"=="help" (
    set "COMMAND=help"
    shift
    goto :parse_loop
)
if "%~1"=="--help" (
    set "COMMAND=--help"
    shift
    goto :parse_loop
)

REM Check for options
if "%~1"=="--port" (
    set "SERVER_PORT=%~2"
    shift
    shift
    goto :parse_loop
)
if "%~1"=="--java-home" (
    set "JAVA_HOME_CUSTOM=%~2"
    shift
    shift
    goto :parse_loop
)
if "%~1"=="--background" (
    set "BACKGROUND_MODE=true"
    shift
    goto :parse_loop
)

echo [WARNING] Unknown argument: %~1
shift
goto :parse_loop

:parse_done
goto :eof

REM ==================== Validation Functions ====================
:validate_port
set "port=%~1"
if "%port%"=="" (
    echo [ERROR] Port number cannot be empty
    exit /b 1
)

REM Check if port is numeric and in valid range (1-65535)
echo %port%| findstr /r "^[0-9][0-9]*$" >nul
if errorlevel 1 (
    echo [ERROR] Port must be a number: %port%
    exit /b 1
)

if %port% LSS 1 (
    echo [ERROR] Port number must be greater than 0: %port%
    exit /b 1
)
if %port% GTR 65535 (
    echo [ERROR] Port number must be less than 65536: %port%
    exit /b 1
)

echo [INFO] Using port: %port%
goto :eof

:validate_java_home
set "java_path=%~1"
if "%java_path%"=="" goto :eof

if not exist "%java_path%\bin\java.exe" (
    echo [ERROR] Java executable not found: %java_path%\bin\java.exe
    exit /b 1
)

echo [INFO] Using custom Java: %java_path%
goto :eof

:setup_java_environment
if not "%JAVA_HOME_CUSTOM%"=="" (
    call :validate_java_home "%JAVA_HOME_CUSTOM%"
    if errorlevel 1 exit /b 1

    set "JAVA_HOME=%JAVA_HOME_CUSTOM%"
    set "PATH=%JAVA_HOME_CUSTOM%\bin;%PATH%"
    set "JAVA_CMD=%JAVA_HOME_CUSTOM%\bin\java.exe"
) else (
    set "JAVA_CMD=java"
)

REM Test Java command
"%JAVA_CMD%" -version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Java not found or not working: %JAVA_CMD%
    exit /b 1
)

echo [INFO] Java validation successful
goto :eof

:init_logging
if "%BACKGROUND_MODE%"=="true" (
    if not exist "%LOG_DIR%" (
        mkdir "%LOG_DIR%"
        echo [INFO] Created log directory: %LOG_DIR%
    )
)
goto :eof

:show_help
echo Usage: %~nx0 [command] [options]
echo.
echo Commands:
echo   start     Start server (default)
echo   stop      Stop server
echo   status    Show server status
echo   help      Show this help
echo.
echo Options:
echo   --port PORT           Specify server port (default: %DEFAULT_SERVER_PORT%)
echo   --java-home PATH      Specify custom Java JDK path
echo   --background          Run server in background
echo.
echo Examples:
echo   %~nx0                                    # Start server with defaults
echo   %~nx0 start                              # Start server with defaults
echo   %~nx0 start --port 8080                  # Start server on port 8080
echo   %~nx0 start --background                 # Start server in background
echo   %~nx0 start --java-home "C:\Java\jdk8"   # Use custom Java
echo   %~nx0 start --port 8080 --background     # Combine options
echo   %~nx0 stop                               # Stop server
echo   %~nx0 status                             # Show status
echo.
exit /b 0

:start_server
echo [INFO] Starting file transfer server...

REM Validate port
call :validate_port "%SERVER_PORT%"
if errorlevel 1 exit /b 1

REM Setup Java environment
call :setup_java_environment
if errorlevel 1 exit /b 1

REM Initialize logging
call :init_logging

REM Check JAR file
if not exist "%JAR_FILE%" (
    echo [ERROR] JAR file not found: %JAR_FILE%
    echo Please run 'mvn clean package' first
    exit /b 1
)

REM Check port availability
netstat -an | findstr ":%SERVER_PORT%" >nul 2>&1
if not errorlevel 1 (
    echo [WARNING] Port %SERVER_PORT% is already in use
    echo Server may already be running
)

REM Build command line
set "JAVA_OPTS=%DEFAULT_JAVA_OPTS%"
set "SPRING_OPTS=--server.port=%SERVER_PORT% --spring.profiles.active=server"
set "FULL_COMMAND="%JAVA_CMD%" %JAVA_OPTS% -jar %JAR_FILE% %SPRING_OPTS%"

echo [INFO] Starting server on port %SERVER_PORT%...
echo [INFO] Java command: %FULL_COMMAND%

if "%BACKGROUND_MODE%"=="true" (
    echo [INFO] Starting server in background mode...
    echo [INFO] Log file: %LOG_FILE%
    echo [INFO] Use '%~nx0 status' to check server status
    echo [INFO] Use '%~nx0 stop' to stop the server
    echo.

    REM Start in background and redirect output to log file
    start /b "" %FULL_COMMAND% > "%LOG_FILE%" 2>&1

    REM Wait for startup (longer wait for background mode)
    echo [INFO] Waiting for server to start...
    timeout /t 8 /nobreak >nul

    REM Check if server started successfully
    netstat -an | findstr ":%SERVER_PORT%" >nul 2>&1
    if not errorlevel 1 (
        echo [SUCCESS] Server started successfully in background
        echo [INFO] Server address: http://localhost:%SERVER_PORT%
        echo [INFO] Health check: http://localhost:%SERVER_PORT%/filetransfer/actuator/health
    ) else (
        echo [WARNING] Server may still be starting. Check log file: %LOG_FILE%
        echo [INFO] Use '%~nx0 status --port %SERVER_PORT%' to check server status
    )
) else (
    echo [INFO] Starting server in foreground mode...
    echo [INFO] Press Ctrl+C to stop server
    echo.

    REM Start in foreground
    %FULL_COMMAND%
)

exit /b %errorlevel%

:stop_server
echo [INFO] Stopping file transfer server...

REM Find Java processes running our JAR
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv ^| findstr "file-transfer-server"') do (
    echo [INFO] Stopping process %%i...
    taskkill /f /pid %%i >nul 2>&1
)

REM Alternative: Kill all Java processes (more aggressive)
echo [INFO] Stopping all Java processes...
taskkill /f /im java.exe >nul 2>&1

echo [SUCCESS] Server stop command completed
exit /b 0

:show_status
echo [INFO] Checking server status...
echo [INFO] Configured port: %SERVER_PORT%

REM Check if port is listening
netstat -an | findstr ":%SERVER_PORT%" >nul 2>&1
if not errorlevel 1 (
    echo [SUCCESS] Server is running on port %SERVER_PORT%
    echo Server address: http://localhost:%SERVER_PORT%
    echo Health check: http://localhost:%SERVER_PORT%/filetransfer/actuator/health

    REM Show log file info if exists
    if exist "%LOG_FILE%" (
        echo Log file: %LOG_FILE%
        echo Log file size:
        for %%A in ("%LOG_FILE%") do echo   %%~zA bytes
    )
) else (
    echo [INFO] Server is not running on port %SERVER_PORT%
)

REM Check Java processes
echo.
echo Java processes:
tasklist /fi "imagename eq java.exe" 2>nul | findstr "java.exe"
if errorlevel 1 (
    echo No Java processes found
)

exit /b 0
